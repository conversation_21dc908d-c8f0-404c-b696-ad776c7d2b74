#!/usr/bin/env node

const OptimizedDataImportService = require('../src/services/OptimizedDataImportService');
const OptimizedSearchService = require('../src/services/OptimizedSearchService');

async function main() {
  console.log('🚀 开始执行优化导入流程...\n');

  try {
    const importService = new OptimizedDataImportService();
    const searchService = new OptimizedSearchService();

    // 1. 执行优化导入
    console.log('📊 步骤1: 执行按视频ID分组的优化导入');
    const importResult = await importService.executeOptimizedImport();
    
    console.log('✅ 导入结果:');
    console.log(`   - 成功: ${importResult.success} 个视频分组`);
    console.log(`   - 失败: ${importResult.failed} 个视频分组`);
    console.log(`   - 总计: ${importResult.total} 个视频分组\n`);

    // 2. 验证优化索引
    console.log('🔍 步骤2: 验证优化索引');
    const indexExists = await searchService.checkOptimizedIndexExists();
    console.log(`   - 分组索引存在: ${indexExists ? '是' : '否'}\n`);

    // 3. 测试优化搜索
    console.log('🧪 步骤3: 测试优化搜索功能');
    
    // 测试视频统计
    const testVideoId = '113599301292265';
    const stats = await searchService.getVideoStats(testVideoId);
    
    if (stats) {
      console.log(`   - 测试视频: ${testVideoId}`);
      console.log(`   - 评论组数: ${stats.total_comment_groups}`);
      console.log(`   - 总评论数: ${stats.total_comments}`);
      console.log(`   - 主评论数: ${stats.total_main_comments}`);
      console.log(`   - 回复数: ${stats.total_replies}`);
      console.log(`   - 索引时间: ${stats.indexed_at}\n`);
    } else {
      console.log(`   - 测试视频 ${testVideoId} 数据未找到\n`);
    }

    // 测试关键词搜索
    console.log('🔎 步骤4: 测试关键词搜索性能');
    const keywords = ['执业药师', '备考'];
    
    console.time('优化搜索耗时');
    const keywordResults = await searchService.searchKeywordCommentsOptimized(
      testVideoId, 
      keywords, 
      5
    );
    console.timeEnd('优化搜索耗时');
    
    console.log(`   - 关键词: ${keywords.join(', ')}`);
    console.log(`   - 找到评论: ${keywordResults.length} 条`);
    
    if (keywordResults.length > 0) {
      console.log('   - 示例结果:');
      keywordResults.slice(0, 2).forEach((comment, index) => {
        console.log(`     ${index + 1}. [${comment.is_reply ? '回复' : '主评论'}] ${comment.content.substring(0, 50)}...`);
      });
    }
    console.log('');

    // 测试最新评论
    console.log('📝 步骤5: 测试最新评论获取');
    
    console.time('获取最新评论耗时');
    const latestComments = await searchService.getLatestCommentsOptimized(testVideoId, 3);
    console.timeEnd('获取最新评论耗时');
    
    console.log(`   - 最新评论: ${latestComments.length} 条`);
    if (latestComments.length > 0) {
      console.log('   - 示例结果:');
      latestComments.forEach((comment, index) => {
        console.log(`     ${index + 1}. [${comment.is_reply ? '回复' : '主评论'}] ${comment.content.substring(0, 50)}...`);
      });
    }
    console.log('');

    console.log('🎉 优化导入和测试完成！');
    console.log('\n📈 性能提升说明:');
    console.log('   - 按视频ID分组存储，避免大范围搜索');
    console.log('   - 预组织评论结构，减少实时计算');
    console.log('   - 单次查询获取完整视频评论，无需分页');
    console.log('   - 支持无限量评论，不受Elasticsearch查询限制');

  } catch (error) {
    console.error('❌ 优化导入失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().then(() => {
    console.log('\n✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('\n❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main };
