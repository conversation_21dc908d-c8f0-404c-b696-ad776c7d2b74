#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Bilibili数据检索系统 - 简化部署 ===${NC}"
echo -e "${YELLOW}(仅包含 Elasticsearch + API 服务)${NC}"
echo

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker运行正常${NC}"

# 停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
docker-compose down -v

# 清理系统
echo -e "${YELLOW}🧹 清理Docker缓存...${NC}"
docker system prune -f

# 构建并启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 15

# 检查服务状态
echo -e "${YELLOW}📊 检查服务状态...${NC}"
docker-compose ps

# 检查Elasticsearch
echo -e "${YELLOW}🔍 检查Elasticsearch...${NC}"
for i in {1..6}; do
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待Elasticsearch启动... ($i/6)${NC}"
        sleep 10
    fi
    
    if [ $i -eq 6 ]; then
        echo -e "${RED}❌ Elasticsearch启动失败${NC}"
        echo -e "${YELLOW}查看日志：${NC}"
        docker-compose logs elasticsearch
        exit 1
    fi
done

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务...${NC}"
for i in {1..3}; do
    if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待API服务启动... ($i/3)${NC}"
        sleep 5
    fi
    
    if [ $i -eq 3 ]; then
        echo -e "${RED}❌ API服务启动失败${NC}"
        echo -e "${YELLOW}查看日志：${NC}"
        docker-compose logs api
        exit 1
    fi
done

echo
echo -e "${GREEN}🎉 部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务地址：${NC}"
echo "• API服务: http://localhost:3000"
echo "• Elasticsearch: http://localhost:9200"
echo
echo -e "${BLUE}🔧 常用命令：${NC}"
echo "• 检查连接: curl http://localhost:3000/api/data/status"
echo "• 导入数据: curl -X POST http://localhost:3000/api/data/refresh"
echo "• 查看日志: docker-compose logs -f"
echo "• 停止服务: docker-compose down"
echo
echo -e "${BLUE}🧪 测试API：${NC}"
echo '• 搜索视频: curl -X POST "http://localhost:3000/api/videos/search" -H "Content-Type: application/json" -d '"'"'{"q": "诺石医考"}'"'"
echo '• 全文搜索: curl -X POST "http://localhost:3000/api/search" -H "Content-Type: application/json" -d '"'"'{"q": "执业药师"}'"'"
echo
echo -e "${YELLOW}💡 下一步：导入数据${NC}"
echo "curl -X POST http://localhost:3000/api/data/refresh"
