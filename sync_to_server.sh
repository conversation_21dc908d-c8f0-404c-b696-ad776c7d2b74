#!/bin/bash

# 同步修改到服务器脚本
SERVER="ubuntu@1.15.151.6"
REMOTE_PATH="/home/<USER>"
LOCAL_FILES=(
    "src/controllers/dataController.js"
    "src/routes/data.js"
    "src/services/OptimizedSearchService.js"
    "test_data_consistency.sh"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_separator() {
    echo -e "${BLUE}================================================${NC}"
}

# 同步文件到服务器
sync_files() {
    print_info "开始同步文件到服务器..."
    
    for file in "${LOCAL_FILES[@]}"; do
        if [ -f "$file" ]; then
            print_info "同步文件: $file"
            
            # 创建远程目录
            remote_dir=$(dirname "$file")
            ssh "$SERVER" "mkdir -p $REMOTE_PATH/$remote_dir"
            
            # 复制文件
            if scp "$file" "$SERVER:$REMOTE_PATH/$file"; then
                print_success "文件同步成功: $file"
            else
                print_error "文件同步失败: $file"
                exit 1
            fi
        else
            print_warning "文件不存在: $file"
        fi
    done
}

# 备份服务器上的原文件
backup_server_files() {
    print_info "备份服务器上的原文件..."
    
    ssh "$SERVER" << 'EOF'
        cd /home/<USER>
        
        # 创建备份目录
        backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        # 备份原文件
        if [ -f "src/controllers/dataController.js" ]; then
            cp "src/controllers/dataController.js" "$backup_dir/"
        fi
        
        if [ -f "src/routes/data.js" ]; then
            cp "src/routes/data.js" "$backup_dir/"
        fi
        
        if [ -f "src/services/OptimizedSearchService.js" ]; then
            cp "src/services/OptimizedSearchService.js" "$backup_dir/"
        fi
        
        echo "备份完成: $backup_dir"
EOF
}

# 重启API服务
restart_api_service() {
    print_info "重启API服务..."
    
    ssh "$SERVER" << 'EOF'
        cd /home/<USER>
        
        # 停止API容器
        echo "停止API容器..."
        docker stop bilibili-api
        
        # 复制修改后的文件到容器挂载目录
        echo "复制修改后的文件..."
        
        # 查找API容器的挂载目录
        api_volume=$(docker inspect bilibili-api | jq -r '.[0].Mounts[] | select(.Destination == "/app") | .Source')
        
        if [ -n "$api_volume" ] && [ "$api_volume" != "null" ]; then
            echo "API容器挂载目录: $api_volume"
            
            # 复制文件到挂载目录
            sudo cp src/controllers/dataController.js "$api_volume/src/controllers/"
            sudo cp src/routes/data.js "$api_volume/src/routes/"
            sudo cp src/services/OptimizedSearchService.js "$api_volume/src/services/"
            
            echo "文件复制完成"
        else
            echo "未找到API容器挂载目录，尝试重新构建镜像..."
            
            # 重新构建API镜像
            docker build -t bilibili-system-offline_api .
        fi
        
        # 启动API容器
        echo "启动API容器..."
        docker start bilibili-api
        
        # 等待服务启动
        echo "等待服务启动..."
        sleep 10
        
        # 检查服务状态
        echo "检查服务状态..."
        docker ps | grep bilibili-api
EOF
}

# 测试API服务
test_api_service() {
    print_info "测试API服务..."
    
    # 等待服务完全启动
    sleep 5
    
    # 测试API状态
    response=$(ssh "$SERVER" "curl -s http://localhost:3000/api/data/status")
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "API服务测试成功"
        echo "$response" | jq '.'
    else
        print_error "API服务测试失败"
        echo "$response"
        
        # 查看容器日志
        print_info "查看容器日志..."
        ssh "$SERVER" "docker logs --tail 20 bilibili-api"
    fi
}

# 运行数据一致性测试
run_consistency_test() {
    print_info "运行数据一致性测试..."
    
    ssh "$SERVER" << 'EOF'
        cd /home/<USER>
        chmod +x test_data_consistency.sh
        ./test_data_consistency.sh
EOF
}

# 主函数
main() {
    print_separator
    echo -e "${BLUE}🚀 开始同步修改到服务器${NC}"
    print_separator
    
    backup_server_files
    echo
    
    sync_files
    echo
    
    restart_api_service
    echo
    
    test_api_service
    echo
    
    run_consistency_test
    echo
    
    print_separator
    echo -e "${GREEN}🎉 同步和测试完成${NC}"
    print_separator
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0"
    echo "同步本地修改的文件到服务器并重启服务进行测试"
    echo ""
    echo "同步的文件:"
    for file in "${LOCAL_FILES[@]}"; do
        echo "  - $file"
    done
    exit 0
fi

# 执行主函数
main
