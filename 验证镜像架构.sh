#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 验证Docker镜像架构 ===${NC}"
echo

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

# 定义需要检查的镜像
IMAGES=(
    "docker.elastic.co/elasticsearch/elasticsearch:8.11.0"
    "node:18-alpine"
    "bilibili-api:latest"
)

echo -e "${YELLOW}🔍 检查镜像架构...${NC}"
echo

for image in "${IMAGES[@]}"; do
    echo -e "${BLUE}检查镜像: $image${NC}"
    
    # 检查镜像是否存在
    if docker image inspect "$image" > /dev/null 2>&1; then
        # 获取镜像架构信息
        ARCH=$(docker image inspect "$image" --format '{{.Architecture}}')
        OS=$(docker image inspect "$image" --format '{{.Os}}')
        
        echo -e "  操作系统: ${YELLOW}$OS${NC}"
        echo -e "  架构: ${YELLOW}$ARCH${NC}"
        
        # 验证是否为x86_64架构
        if [ "$ARCH" = "amd64" ] && [ "$OS" = "linux" ]; then
            echo -e "  状态: ${GREEN}✅ 符合要求 (linux/amd64)${NC}"
        else
            echo -e "  状态: ${RED}❌ 架构不匹配，期望: linux/amd64，实际: $OS/$ARCH${NC}"
        fi
    else
        echo -e "  状态: ${RED}❌ 镜像不存在${NC}"
    fi
    echo
done

echo -e "${BLUE}📊 系统架构信息：${NC}"
echo -e "当前系统架构: ${YELLOW}$(uname -m)${NC}"
echo -e "Docker版本: ${YELLOW}$(docker --version)${NC}"

# 检查Docker是否支持多架构
echo
echo -e "${BLUE}🔧 Docker多架构支持：${NC}"
if docker buildx version > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 支持Docker Buildx (多架构构建)${NC}"
    echo -e "可用平台: ${YELLOW}$(docker buildx ls | grep -E 'linux/(amd64|arm64)' | head -1)${NC}"
else
    echo -e "${YELLOW}⚠️  未检测到Docker Buildx${NC}"
fi

echo
echo -e "${BLUE}💡 提示：${NC}"
echo "• 目标部署环境应为 linux/amd64 (x86_64) 架构"
echo "• 如果当前系统为ARM架构，镜像仍可在x86系统上运行"
echo "• 打包的镜像已指定为 linux/amd64 架构"
