#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 测试Docker镜像源连通性 ===${NC}"
echo

# 镜像源列表
MIRRORS=(
    "https://registry.cn-hangzhou.aliyuncs.com"
    "https://docker.m.daocloud.io"
    "https://dockerhub.azk8s.cn"
    "https://reg-mirror.qiniu.com"
)

# 测试每个镜像源
for mirror in "${MIRRORS[@]}"; do
    echo -e "${YELLOW}测试镜像源: $mirror${NC}"
    
    # 测试连通性
    if curl -s --connect-timeout 5 --max-time 10 "$mirror/v2/" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $mirror - 连接正常${NC}"
    else
        echo -e "${RED}❌ $mirror - 连接失败${NC}"
    fi
    echo
done

echo -e "${BLUE}=== 当前Docker配置 ===${NC}"
if docker info | grep -A 10 "Registry Mirrors" 2>/dev/null; then
    echo
else
    echo -e "${RED}❌ 无法获取Docker信息，请确认Docker正在运行${NC}"
fi

echo -e "${BLUE}=== 测试镜像拉取 ===${NC}"
echo -e "${YELLOW}测试拉取hello-world镜像...${NC}"

if docker pull hello-world > /dev/null 2>&1; then
    echo -e "${GREEN}✅ hello-world镜像拉取成功${NC}"
    docker rmi hello-world > /dev/null 2>&1
else
    echo -e "${RED}❌ hello-world镜像拉取失败${NC}"
fi

echo
echo -e "${YELLOW}测试拉取node:18-alpine镜像...${NC}"

if timeout 60 docker pull node:18-alpine > /dev/null 2>&1; then
    echo -e "${GREEN}✅ node:18-alpine镜像拉取成功${NC}"
else
    echo -e "${RED}❌ node:18-alpine镜像拉取失败或超时${NC}"
fi

echo
echo -e "${BLUE}=== 建议 ===${NC}"
echo "如果所有镜像源都连接失败，请检查："
echo "1. 网络连接是否正常"
echo "2. 防火墙设置"
echo "3. 代理配置"
echo "4. DNS设置"
