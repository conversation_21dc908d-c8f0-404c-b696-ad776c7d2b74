#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 创建最终部署包 ===${NC}"
echo

# 创建最终部署目录
FINAL_PACKAGE="bilibili-deployment-package"
echo -e "${YELLOW}📁 创建部署包目录: $FINAL_PACKAGE${NC}"
rm -rf "$FINAL_PACKAGE"
mkdir -p "$FINAL_PACKAGE"

# 复制离线系统包
echo -e "${YELLOW}📦 复制离线系统包...${NC}"
if [ -f "bilibili-system-offline.tar.gz" ]; then
    cp bilibili-system-offline.tar.gz "$FINAL_PACKAGE/"
    echo -e "${GREEN}✅ 离线系统包已复制${NC}"
else
    echo -e "${RED}❌ 未找到离线系统包${NC}"
    exit 1
fi

# 复制部署指南
echo -e "${YELLOW}📋 复制部署文档...${NC}"
cp 离线部署指南.md "$FINAL_PACKAGE/"

# 创建快速部署脚本
echo -e "${YELLOW}📝 创建快速部署脚本...${NC}"

cat > "$FINAL_PACKAGE/快速部署.sh" << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Bilibili数据检索系统 - 快速部署 ===${NC}"
echo

# 检查系统要求
echo -e "${YELLOW}🔍 检查系统要求...${NC}"

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "x86_64" ]; then
    echo -e "${RED}❌ 系统架构不匹配，需要 x86_64，当前: $ARCH${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 系统架构: $ARCH${NC}"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker已安装: $(docker --version)${NC}"

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker Compose已安装: $(docker-compose --version)${NC}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker服务${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker服务运行正常${NC}"

# 检查端口占用
echo -e "${YELLOW}🔍 检查端口占用...${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
    echo -e "${RED}❌ 端口3000已被占用${NC}"
    exit 1
fi

if netstat -tlnp 2>/dev/null | grep -q ":9200 "; then
    echo -e "${RED}❌ 端口9200已被占用${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 端口检查通过${NC}"

echo

# 解压系统包
echo -e "${YELLOW}📦 解压系统包...${NC}"
if [ ! -f "bilibili-system-offline.tar.gz" ]; then
    echo -e "${RED}❌ 未找到系统包文件${NC}"
    exit 1
fi

tar -xzf bilibili-system-offline.tar.gz
cd bilibili-system-offline

# 加载Docker镜像
echo -e "${YELLOW}🐳 加载Docker镜像...${NC}"
echo "这可能需要几分钟时间，请耐心等待..."

for tar_file in docker-images/*.tar; do
    if [ -f "$tar_file" ]; then
        echo -e "${BLUE}加载: $(basename "$tar_file")${NC}"
        if docker load -i "$tar_file"; then
            echo -e "${GREEN}✅ 加载成功${NC}"
        else
            echo -e "${RED}❌ 加载失败: $tar_file${NC}"
            exit 1
        fi
    fi
done

echo

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose down -v 2>/dev/null
docker-compose up -d

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo -e "${YELLOW}查看错误日志:${NC}"
    docker-compose logs
    exit 1
fi

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 20

# 检查Elasticsearch
echo -e "${YELLOW}🔍 检查Elasticsearch...${NC}"
for i in {1..12}; do
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待Elasticsearch启动... ($i/12)${NC}"
        sleep 10
    fi
    
    if [ $i -eq 12 ]; then
        echo -e "${RED}❌ Elasticsearch启动超时${NC}"
        echo -e "${YELLOW}查看日志:${NC}"
        docker-compose logs elasticsearch
        exit 1
    fi
done

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务...${NC}"
for i in {1..6}; do
    if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待API服务启动... ($i/6)${NC}"
        sleep 5
    fi
    
    if [ $i -eq 6 ]; then
        echo -e "${RED}❌ API服务启动超时${NC}"
        echo -e "${YELLOW}查看日志:${NC}"
        docker-compose logs api
        exit 1
    fi
done

echo
echo -e "${GREEN}🎉 部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务地址：${NC}"
echo "• API服务: http://localhost:3000"
echo "• Elasticsearch: http://localhost:9200"
echo
echo -e "${BLUE}🔧 下一步操作：${NC}"
echo "1. 导入数据:"
echo "   curl -X POST http://localhost:3000/api/data/refresh"
echo
echo "2. 测试API:"
echo '   curl -X POST "http://localhost:3000/api/videos/search" -H "Content-Type: application/json" -d '"'"'{"q": "诺石医考"}'"'"
echo
echo "3. 查看服务状态:"
echo "   docker-compose ps"
echo
echo "4. 查看日志:"
echo "   docker-compose logs -f"
echo
echo -e "${YELLOW}💡 提示：首次使用需要导入数据，这可能需要几分钟时间${NC}"
EOF

# 创建数据导入脚本
cat > "$FINAL_PACKAGE/导入数据.sh" << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 导入Bilibili数据 ===${NC}"
echo

# 进入系统目录
if [ -d "bilibili-system-offline" ]; then
    cd bilibili-system-offline
else
    echo -e "${RED}❌ 未找到系统目录，请先运行快速部署脚本${NC}"
    exit 1
fi

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务状态...${NC}"
if ! curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
    echo -e "${RED}❌ API服务未运行，请先运行快速部署脚本${NC}"
    exit 1
fi

echo -e "${GREEN}✅ API服务运行正常${NC}"

# 显示当前数据状态
echo -e "${YELLOW}📊 当前数据状态:${NC}"
curl -s http://localhost:3000/api/data/stats | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/stats

echo
echo -e "${YELLOW}📥 开始导入数据...${NC}"
echo "这可能需要几分钟时间，请耐心等待..."
echo

# 导入数据
if curl -X POST http://localhost:3000/api/data/refresh; then
    echo
    echo -e "${GREEN}🎉 数据导入完成！${NC}"
    echo
    echo -e "${BLUE}📊 最新数据统计：${NC}"
    curl -s http://localhost:3000/api/data/stats | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/stats
    echo
    echo -e "${BLUE}🧪 测试搜索功能：${NC}"
    echo '• 搜索视频: curl -X POST "http://localhost:3000/api/videos/search" -H "Content-Type: application/json" -d '"'"'{"q": "诺石医考"}'"'"
    echo '• 全文搜索: curl -X POST "http://localhost:3000/api/search" -H "Content-Type: application/json" -d '"'"'{"q": "执业药师"}'"'"
else
    echo
    echo -e "${RED}❌ 数据导入失败${NC}"
    echo -e "${YELLOW}请检查：${NC}"
    echo "1. MySQL数据库连接是否正常"
    echo "2. 网络连接是否正常"
    echo "3. 查看API服务日志: docker-compose logs api"
    exit 1
fi
EOF

# 创建停止服务脚本
cat > "$FINAL_PACKAGE/停止服务.sh" << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 停止Bilibili数据检索系统 ===${NC}"
echo

# 进入系统目录
if [ -d "bilibili-system-offline" ]; then
    cd bilibili-system-offline
else
    echo -e "${RED}❌ 未找到系统目录${NC}"
    exit 1
fi

echo -e "${YELLOW}🛑 停止所有服务...${NC}"
docker-compose down

echo -e "${YELLOW}🧹 清理容器和网络...${NC}"
docker-compose down -v

echo -e "${GREEN}✅ 服务已停止${NC}"
echo
echo -e "${BLUE}💡 如需重新启动服务，请运行:${NC}"
echo "cd bilibili-system-offline && docker-compose up -d"
EOF

# 设置脚本执行权限
chmod +x "$FINAL_PACKAGE"/*.sh

# 创建部署说明文件
cat > "$FINAL_PACKAGE/部署说明.txt" << 'EOF'
Bilibili数据检索系统 - 部署包说明
=====================================

📦 包含文件：
- bilibili-system-offline.tar.gz  (完整系统包，2.0GB)
- 快速部署.sh                     (一键部署脚本)
- 导入数据.sh                     (数据导入脚本)
- 停止服务.sh                     (停止服务脚本)
- 离线部署指南.md                 (详细部署文档)
- 部署说明.txt                    (本文件)

🚀 快速部署步骤：
1. 上传整个部署包到服务器
2. 解压: tar -xzf bilibili-deployment-package.tar.gz
3. 进入目录: cd bilibili-deployment-package
4. 运行部署: ./快速部署.sh
5. 导入数据: ./导入数据.sh

⚠️ 系统要求：
- Linux x86_64 系统
- Docker 20.10+
- Docker Compose 1.29+
- 4GB+ 内存
- 10GB+ 存储空间
- 端口 3000, 9200 未被占用

📋 服务地址：
- API服务: http://localhost:3000
- Elasticsearch: http://localhost:9200

🔧 常用命令：
- 查看服务状态: docker-compose ps
- 查看日志: docker-compose logs -f
- 重启服务: docker-compose restart
- 停止服务: ./停止服务.sh

📚 详细说明请查看: 离线部署指南.md
EOF

echo

# 创建最终压缩包
echo -e "${YELLOW}📦 创建最终部署压缩包...${NC}"
tar -czf bilibili-deployment-package.tar.gz "$FINAL_PACKAGE"

# 获取文件大小
PACKAGE_SIZE=$(du -h bilibili-deployment-package.tar.gz | cut -f1)

echo
echo -e "${GREEN}🎉 部署包创建完成！${NC}"
echo
echo -e "${BLUE}📦 部署包信息：${NC}"
echo "• 文件名: bilibili-deployment-package.tar.gz"
echo "• 大小: $PACKAGE_SIZE"
echo "• 架构: x86_64 (amd64)"
echo
echo -e "${BLUE}📋 包含内容：${NC}"
echo "• 完整Docker镜像包 (2.0GB)"
echo "• 一键部署脚本"
echo "• 数据导入脚本"
echo "• 服务管理脚本"
echo "• 详细部署文档"
echo
echo -e "${BLUE}🚀 服务器部署步骤：${NC}"
echo "1. 上传 bilibili-deployment-package.tar.gz 到服务器"
echo "2. 解压: tar -xzf bilibili-deployment-package.tar.gz"
echo "3. 进入目录: cd bilibili-deployment-package"
echo "4. 运行部署: ./快速部署.sh"
echo "5. 导入数据: ./导入数据.sh"
echo
echo -e "${YELLOW}💡 提示：确保目标服务器已安装Docker和Docker Compose${NC}"
