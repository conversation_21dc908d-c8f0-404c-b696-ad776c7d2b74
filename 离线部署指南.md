# Bilibili数据检索系统 - 离线部署指南

## 📦 打包信息

- **文件名**: `bilibili-system-offline.tar.gz`
- **大小**: 2.0GB
- **架构**: x86_64 (amd64)
- **包含内容**: 完整的Docker镜像 + 源代码 + 配置文件

## 🎯 系统要求

### 目标服务器要求
- **操作系统**: Linux (推荐 Ubuntu 18.04+, CentOS 7+)
- **架构**: x86_64 (amd64)
- **内存**: 最少 4GB RAM (推荐 8GB+)
- **存储**: 最少 10GB 可用空间
- **软件**: Docker 20.10+ 和 Docker Compose 1.29+

### 端口要求
- **3000**: API服务端口
- **9200**: Elasticsearch端口
- **9300**: Elasticsearch集群通信端口

## 🚀 部署步骤

### 1. 传输文件到目标服务器
```bash
# 使用scp传输（示例）
scp bilibili-system-offline.tar.gz user@target-server:/opt/

# 或使用其他方式传输到目标服务器
```

### 2. 解压部署包
```bash
cd /opt
tar -xzf bilibili-system-offline.tar.gz
cd bilibili-system-offline
```

### 3. 检查目录结构
```bash
ls -la
# 应该看到以下文件和目录：
# - docker-images/     (Docker镜像文件)
# - scripts/          (部署脚本)
# - src/              (源代码)
# - docker-compose.yml (容器编排配置)
# - Dockerfile        (镜像构建文件)
# - .env              (环境变量配置)
# - README.md         (说明文档)
```

### 4. 运行离线部署
```bash
# 设置脚本执行权限
chmod +x scripts/*.sh

# 执行离线部署
./scripts/离线部署.sh
```

### 5. 导入数据
```bash
# 等待服务完全启动后执行
./scripts/导入数据.sh
```

### 6. 验证部署
```bash
# 测试系统功能
./scripts/测试系统.sh

# 手动检查服务状态
curl http://localhost:3000/api/data/status
curl http://localhost:9200/_cluster/health
```

## 🔧 手动部署步骤（如果脚本失败）

### 1. 加载Docker镜像
```bash
# 加载Elasticsearch镜像
docker load -i docker-images/docker.elastic.co_elasticsearch_elasticsearch_8.11.0.tar

# 加载Node.js镜像
docker load -i docker-images/node_18-alpine.tar

# 加载API镜像（如果存在）
docker load -i docker-images/bilibili-api_latest.tar
```

### 2. 构建API镜像（如果需要）
```bash
docker build --platform linux/amd64 -t bilibili-api:latest .
```

### 3. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 4. 检查日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs elasticsearch
docker-compose logs api
```

## 📋 服务地址

部署成功后，可以通过以下地址访问服务：

- **API服务**: http://localhost:3000
- **Elasticsearch**: http://localhost:9200

## 🧪 测试API

### 基础测试
```bash
# 检查连接状态
curl http://localhost:3000/api/data/status

# 查看数据统计
curl http://localhost:3000/api/data/stats
```

### 功能测试
```bash
# 搜索视频
curl -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "limit": 5}'

# 全文搜索
curl -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "type": "all", "limit": 5}'

# 关键词评论分析
curl -X POST "http://localhost:3000/api/comments/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师"], "limit": 10}'
```

## 🔧 常用管理命令

```bash
# 查看服务状态
docker-compose ps

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 查看资源使用情况
docker stats

# 清理未使用的镜像和容器
docker system prune -f
```

## 📚 文档说明

- **API_DOCUMENTATION.md**: 完整的API接口文档
- **POST_API_USAGE_GUIDE.md**: POST方法使用指南
- **README.md**: 项目说明文档

## 🎯 数据说明

系统包含以下数据：
- **45个视频**: 来自Bilibili的视频信息
- **4105条评论**: 包含一级评论和回复评论
- **支持中文搜索**: 全文检索和关键词匹配
- **评论分析**: 重复率计算和关键词提取

## ⚠️ 注意事项

1. **首次启动**: Elasticsearch需要1-2分钟初始化时间
2. **内存要求**: 确保系统有足够内存运行Elasticsearch
3. **端口冲突**: 确保3000和9200端口未被占用
4. **数据导入**: 首次部署后需要手动导入数据
5. **架构兼容**: 镜像已针对x86_64架构优化

## 🆘 故障排除

### 服务启动失败
```bash
# 查看详细日志
docker-compose logs

# 检查端口占用
netstat -tlnp | grep -E ':(3000|9200|9300)'

# 重新构建镜像
docker-compose build --no-cache
```

### 数据导入失败
```bash
# 检查MySQL连接配置
cat .env | grep MYSQL

# 手动测试数据库连接
curl http://localhost:3000/api/data/status
```

### 内存不足
```bash
# 调整Elasticsearch内存设置
# 编辑 docker-compose.yml 中的 ES_JAVA_OPTS
# 例如：ES_JAVA_OPTS=-Xms512m -Xmx512m
```

## 📞 技术支持

如遇到部署问题，请检查：
1. Docker和Docker Compose版本
2. 系统资源使用情况
3. 网络端口占用情况
4. 服务日志输出信息
