#!/bin/bash

# 数据管理API使用示例脚本
# 使用方法: ./数据管理示例.sh [命令]
# 命令选项: status, stats, clear, import, refresh

API_BASE="http://localhost:3000/api/data"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查连接状态
check_status() {
    print_info "检查数据库连接状态..."
    curl -s -X GET "$API_BASE/status" | jq '.'
}

# 获取数据统计
get_stats() {
    print_info "获取数据统计信息..."
    curl -s -X GET "$API_BASE/stats" | jq '.'
}

# 清空数据
clear_data() {
    print_warning "即将清空Elasticsearch中的所有数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "清空Elasticsearch数据..."
        curl -s -X POST "$API_BASE/clear" | jq '.'
        print_success "数据清空完成"
    else
        print_info "操作已取消"
    fi
}

# 导入数据
import_data() {
    print_info "开始导入最新数据..."
    curl -s -X POST "$API_BASE/import" | jq '.'
    print_success "数据导入完成"
}

# 刷新所有数据
refresh_data() {
    print_warning "即将清空并重新导入所有数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "刷新所有数据..."
        curl -s -X POST "$API_BASE/refresh" | jq '.'
        print_success "数据刷新完成"
    else
        print_info "操作已取消"
    fi
}

# 显示帮助信息
show_help() {
    echo "数据管理API使用示例"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  status   - 检查数据库连接状态"
    echo "  stats    - 获取数据统计信息"
    echo "  clear    - 清空Elasticsearch数据"
    echo "  import   - 导入最新数据"
    echo "  refresh  - 清空并重新导入所有数据"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status    # 检查连接状态"
    echo "  $0 stats     # 查看数据统计"
    echo "  $0 refresh   # 完全刷新数据"
}

# 主逻辑
case "$1" in
    "status")
        check_status
        ;;
    "stats")
        get_stats
        ;;
    "clear")
        clear_data
        ;;
    "import")
        import_data
        ;;
    "refresh")
        refresh_data
        ;;
    "help"|"")
        show_help
        ;;
    *)
        print_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac
