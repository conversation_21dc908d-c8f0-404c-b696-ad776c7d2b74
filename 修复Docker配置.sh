#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 修复Docker镜像源配置 ===${NC}"
echo

# 检测操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    DOCKER_CONFIG="$HOME/.docker/daemon.json"
    echo -e "${YELLOW}检测到macOS系统${NC}"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    DOCKER_CONFIG="/etc/docker/daemon.json"
    echo -e "${YELLOW}检测到Linux系统${NC}"
else
    echo -e "${RED}不支持的操作系统${NC}"
    exit 1
fi

echo -e "${YELLOW}Docker配置文件路径: $DOCKER_CONFIG${NC}"

# 备份现有配置
if [ -f "$DOCKER_CONFIG" ]; then
    echo -e "${YELLOW}备份现有配置...${NC}"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo cp "$DOCKER_CONFIG" "$DOCKER_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
    else
        cp "$DOCKER_CONFIG" "$DOCKER_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    echo -e "${GREEN}✅ 配置已备份${NC}"
fi

# 创建新的配置
echo -e "${YELLOW}创建新的Docker配置...${NC}"

NEW_CONFIG='{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}'

# 写入配置文件
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "$NEW_CONFIG" | sudo tee "$DOCKER_CONFIG" > /dev/null
else
    # 确保目录存在
    mkdir -p "$(dirname "$DOCKER_CONFIG")"
    echo "$NEW_CONFIG" > "$DOCKER_CONFIG"
fi

echo -e "${GREEN}✅ 新配置已写入${NC}"

# 显示配置内容
echo -e "${BLUE}当前配置内容：${NC}"
cat "$DOCKER_CONFIG"

echo
echo -e "${YELLOW}⚠️  重要：需要重启Docker服务使配置生效${NC}"
echo

if [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "${BLUE}macOS用户：${NC}"
    echo "1. 点击Docker Desktop图标"
    echo "2. 选择 'Restart Docker Desktop'"
    echo "3. 等待Docker重启完成"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo -e "${BLUE}Linux用户：${NC}"
    echo "sudo systemctl restart docker"
    echo
    read -p "是否现在重启Docker服务？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo systemctl restart docker
        echo -e "${GREEN}✅ Docker服务已重启${NC}"
    fi
fi

echo
echo -e "${GREEN}🎉 配置修复完成！${NC}"
echo -e "${YELLOW}重启Docker后，请运行以下命令验证：${NC}"
echo "docker info | grep -A 5 'Registry Mirrors'"
