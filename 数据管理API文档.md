# 数据管理API文档

## 概述

本文档介绍了用于管理MySQL与Elasticsearch之间数据同步的API接口。通过这些接口，您可以：

- 检查数据库连接状态
- 查看数据同步状态
- 清空检索系统数据
- 导入最新数据
- 完全刷新数据

## 当前数据流架构

### 数据存储架构
```
MySQL (源数据库)  ←→  Elasticsearch (检索引擎)
     ↓                        ↓
- bilibili_video         - bilibili_videos (索引)
- bilibili_video_comment - bilibili_comments (索引)
```

### 数据同步方式
- **手动导入**：通过命令行脚本或API接口触发
- **非实时同步**：需要手动触发数据导入
- **批量处理**：每批处理1000条记录

## API接口详情

### 1. 检查数据库连接状态

**接口**: `GET /api/data/status`

**功能**: 检查MySQL和Elasticsearch的连接状态

**响应示例**:
```json
{
  "success": true,
  "data": {
    "mysql": {
      "connected": true,
      "status": "connected"
    },
    "elasticsearch": {
      "connected": true,
      "status": "connected"
    }
  }
}
```

### 2. 获取数据统计信息

**接口**: `GET /api/data/stats`

**功能**: 获取MySQL和Elasticsearch中的数据统计，以及同步状态

**响应示例**:
```json
{
  "success": true,
  "data": {
    "mysql": {
      "videos": 45,
      "comments": 4105,
      "total": 4150
    },
    "elasticsearch": {
      "videos": 45,
      "comments": 4105,
      "total": 4150
    },
    "sync_status": {
      "videos_synced": true,
      "comments_synced": true,
      "fully_synced": true
    }
  }
}
```

### 3. 清空Elasticsearch数据

**接口**: `POST /api/data/clear`

**功能**: 清空Elasticsearch中的所有索引数据（重建索引结构）

**响应示例**:
```json
{
  "success": true,
  "message": "Elasticsearch数据已清空",
  "data": {
    "videos": 0,
    "comments": 0,
    "cleared_at": "2025-07-07T12:00:00.000Z"
  }
}
```

### 4. 导入最新数据

**接口**: `POST /api/data/import`

**功能**: 从MySQL导入最新数据到Elasticsearch（增量导入）

**响应示例**:
```json
{
  "success": true,
  "message": "数据导入完成",
  "data": {
    "import_result": {
      "videos": {
        "success": 45,
        "failed": 0,
        "total": 45
      },
      "comments": {
        "success": 4105,
        "failed": 0,
        "total": 4105
      }
    },
    "before_stats": {
      "videos": 0,
      "comments": 0
    },
    "after_stats": {
      "videos": 45,
      "comments": 4105
    },
    "imported_at": "2025-07-07T12:00:00.000Z"
  }
}
```

### 5. 完全刷新数据

**接口**: `POST /api/data/refresh`

**功能**: 清空Elasticsearch数据并重新导入所有数据（全量刷新）

**响应示例**:
```json
{
  "success": true,
  "message": "数据刷新完成",
  "data": {
    "import_result": {
      "videos": {
        "success": 45,
        "failed": 0,
        "total": 45
      },
      "comments": {
        "success": 4105,
        "failed": 0,
        "total": 4105
      }
    },
    "final_stats": {
      "videos": 45,
      "comments": 4105
    },
    "refreshed_at": "2025-07-07T12:00:00.000Z"
  }
}
```

## 使用场景

### 1. 定期数据同步
```bash
# 检查同步状态
curl -X GET "http://localhost:3000/api/data/stats"

# 如果数据不同步，执行导入
curl -X POST "http://localhost:3000/api/data/import"
```

### 2. 完全重建索引
```bash
# 清空并重新导入所有数据
curl -X POST "http://localhost:3000/api/data/refresh"
```

### 3. 故障排查
```bash
# 检查连接状态
curl -X GET "http://localhost:3000/api/data/status"

# 查看数据统计
curl -X GET "http://localhost:3000/api/data/stats"
```

## 错误处理

所有API在出错时都会返回以下格式：
```json
{
  "success": false,
  "error": "错误描述",
  "details": "详细错误信息"
}
```

## 注意事项

1. **数据导入时间**: 大量数据导入可能需要较长时间，请耐心等待
2. **并发限制**: 避免同时执行多个数据导入操作
3. **数据一致性**: 导入过程中避免修改MySQL数据
4. **监控建议**: 定期检查同步状态，确保数据一致性

## 与n8n集成建议

在n8n工作流中，您可以：

1. **定时检查**: 使用Cron节点定期调用 `/api/data/stats` 检查同步状态
2. **条件导入**: 根据 `sync_status.fully_synced` 字段决定是否需要导入数据
3. **错误通知**: 在导入失败时发送通知
4. **数据验证**: 导入后验证数据完整性

## 命令行工具

提供了便捷的Shell脚本 `数据管理示例.sh` 用于快速操作：

```bash
# 检查连接状态
./数据管理示例.sh status

# 查看数据统计
./数据管理示例.sh stats

# 清空数据
./数据管理示例.sh clear

# 导入数据
./数据管理示例.sh import

# 完全刷新
./数据管理示例.sh refresh
```

## 总结

### 当前数据同步机制
- **同步方式**: 手动触发（非实时）
- **数据源**: MySQL数据库 (**********:3307)
- **检索引擎**: Elasticsearch (localhost:9200)
- **数据量**: 45个视频，4105条评论

### 推荐的数据管理流程
1. **定期检查**: 使用 `GET /api/data/stats` 检查同步状态
2. **按需导入**: 当 `fully_synced: false` 时，调用 `POST /api/data/import`
3. **故障恢复**: 出现问题时使用 `POST /api/data/refresh` 完全重建
4. **监控告警**: 集成到监控系统中，自动化数据同步流程
