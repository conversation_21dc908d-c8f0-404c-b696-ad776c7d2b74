{"name": "bilibili-elasticsearch-api", "version": "1.0.0", "description": "Bilibili视频和评论数据的Elasticsearch搜索API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "import": "node src/scripts/import-data.js", "test": "jest"}, "keywords": ["elasticsearch", "bilibili", "api", "search"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "@elastic/elasticsearch": "^8.11.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}