# API输出数据结构说明

## 文件信息
- **文件名**: `输出.json`
- **文件大小**: 15,068 字节
- **格式**: JSON（已格式化，具有清晰的缩进结构）
- **时区**: 东八区 (UTC+8)

## 数据结构概览

```json
{
  "success": true,
  "data": {
    "video": { ... },           // 视频基本信息
    "stats": { ... },           // 统计信息
    "latestComments": [ ... ],  // 最新评论列表
    "keywordComments": [ ... ]  // 关键词相关评论列表
  }
}
```

## 详细字段说明

### 1. 根级别字段
- `success`: 布尔值，表示API调用是否成功
- `data`: 对象，包含所有返回的数据

### 2. video 对象 - 视频信息
```json
{
  "id": 331,                                    // 内部ID
  "user_id": "691521358",                       // 用户ID
  "nickname": "至尊菠萝牛肉芝士汉堡",              // 用户昵称
  "avatar": "https://...",                      // 用户头像URL
  "add_ts": 1751862635782,                      // 添加时间戳（毫秒）
  "last_modify_ts": 1751862635780,              // 最后修改时间戳（毫秒）
  "video_id": "113599301292265",                // 视频ID
  "av_number": "113599301292265",               // AV号
  "video_type": "video",                        // 视频类型
  "title": "VLOG打卡|跟着诺石打卡执业药师第四天~|制定学习计划|诺石医考",
  "desc": "-",                                  // 视频描述
  "create_time": 1733388051,                    // 创建时间戳（秒）
  "create_date": "2024-12-05T16:40:51.000+08:00", // 创建日期（东八区）
  "liked_count": 2,                             // 点赞数
  "video_play_count": 11035,                    // 播放数
  "video_danmaku": 0,                           // 弹幕数
  "video_comment": 83,                          // 评论数
  "video_url": "https://www.bilibili.com/video/av113599301292265",
  "video_cover_url": "http://...",              // 封面图URL
  "source_keyword": "",                         // 来源关键词
  "indexed_at": "2025-07-07T19:49:00.928+08:00", // 索引时间（东八区）
  "_id": "331",                                 // Elasticsearch文档ID
  "_score": 7.8463526,                          // 搜索相关性评分
  "engagement_rate": "0.75",                    // 互动率
  "hot_index": 1523                             // 热度指数
}
```

### 3. stats 对象 - 统计信息
```json
{
  "totalComments": 83,                          // 总评论数
  "duplicateRate": 5,                           // 重复率（百分比）
  "keywordCommentGroups": 5,                    // 关键词评论组数
  "total": 83,                                  // 总数
  "uniqueUsers": 0,                             // 唯一用户数
  "likeStats": {                                // 点赞统计
    "avg": 0,                                   // 平均值
    "max": 0,                                   // 最大值
    "min": 0,                                   // 最小值
    "sum": 0                                    // 总和
  },
  "contentLengthStats": {                       // 内容长度统计
    "avg": 42,                                  // 平均长度
    "max": 167,                                 // 最大长度
    "min": 5                                    // 最小长度
  },
  "replyStats": {                               // 回复统计
    "replies": 0,                               // 回复数
    "directComments": 0                         // 直接评论数
  },
  "keywordStats": []                            // 关键词统计（数组）
}
```

### 4. latestComments 数组 - 最新评论
每个评论对象包含以下字段：
```json
{
  "id": 57896,                                  // 内部ID
  "user_id": "3493256348305542",                // 用户ID
  "nickname": "菩提子霸王",                      // 用户昵称
  "avatar": "https://...",                      // 用户头像
  "comment_id": "267494640560",                 // 评论ID
  "video_id": "113599301292265",                // 视频ID
  "content": "有时候就是这么简单...",              // 评论内容
  "content_length": 34,                         // 内容长度
  "create_time": 1751537715,                    // 创建时间戳（秒）
  "create_date": "2025-07-03T18:15:15.000+08:00", // 创建日期（东八区）
  "sub_comment_count": 0,                       // 子评论数
  "parent_comment_id": "267492731856",          // 父评论ID（"0"表示主评论）
  "like_count": 0,                              // 点赞数
  "is_reply": true,                             // 是否为回复
  "keywords": [],                               // 关键词数组
  "indexed_at": "2025-07-07T19:49:01.104+08:00" // 索引时间（东八区）
}
```

### 5. keywordComments 数组 - 关键词相关评论
结构与 `latestComments` 相同，但这些评论包含指定的关键词。
关键词会在 `keywords` 字段中显示，例如：
```json
{
  "keywords": ["执业药师", "备考"]               // 匹配的关键词
}
```

## 时间格式说明

所有时间字段都使用东八区时间格式：
- **格式**: `YYYY-MM-DDTHH:mm:ss.sss+08:00`
- **示例**: `"2024-12-05T16:40:51.000+08:00"`
- **说明**: 比UTC时间早8小时

## 数据查看建议

1. **使用JSON查看器**: 推荐使用支持JSON格式的编辑器或在线工具
2. **关键数据位置**:
   - 视频基本信息: `data.video`
   - 统计摘要: `data.stats`
   - 关键词评论: `data.keywordComments`
3. **重要指标**:
   - 关键词评论组数: `data.stats.keywordCommentGroups`
   - 重复率: `data.stats.duplicateRate`
   - 总评论数: `data.stats.totalComments`

## 文件使用方法

```bash
# 查看文件结构
head -50 输出.json

# 提取特定数据（需要jq工具）
jq '.data.stats' 输出.json                    # 查看统计信息
jq '.data.keywordComments[0]' 输出.json       # 查看第一个关键词评论
jq '.data.video.title' 输出.json              # 查看视频标题
```
