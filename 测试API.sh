#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== API功能测试 ===${NC}"
echo

# 检查API服务状态
echo -e "${YELLOW}🔍 检查API服务状态...${NC}"
if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
    echo -e "${GREEN}✅ API服务运行正常${NC}"
else
    echo -e "${RED}❌ API服务未运行，请先启动服务${NC}"
    exit 1
fi

echo

# 1. 检查连接状态
echo -e "${BLUE}1. 检查数据库连接状态${NC}"
curl -s http://localhost:3000/api/data/status | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/status
echo
echo

# 2. 查看数据统计
echo -e "${BLUE}2. 查看数据统计${NC}"
curl -s http://localhost:3000/api/data/stats | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/stats
echo
echo

# 3. 测试视频搜索
echo -e "${BLUE}3. 测试视频搜索 (搜索关键词: 诺石医考)${NC}"
curl -s -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "limit": 3}' | jq '.' 2>/dev/null || \
curl -s -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "limit": 3}'
echo
echo

# 4. 测试全文搜索
echo -e "${BLUE}4. 测试全文搜索 (搜索关键词: 执业药师)${NC}"
curl -s -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "type": "all", "limit": 2}' | jq '.' 2>/dev/null || \
curl -s -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "type": "all", "limit": 2}'
echo
echo

# 5. 测试评论搜索
echo -e "${BLUE}5. 测试评论搜索 (搜索关键词: 老师)${NC}"
curl -s -X POST "http://localhost:3000/api/comments/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "老师", "limit": 2}' | jq '.' 2>/dev/null || \
curl -s -X POST "http://localhost:3000/api/comments/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "老师", "limit": 2}'
echo
echo

# 6. 测试关键词评论分析
echo -e "${BLUE}6. 测试关键词评论分析 (关键词: 执业药师)${NC}"
curl -s -X POST "http://localhost:3000/api/comments/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师"], "limit": 2}' | jq '.' 2>/dev/null || \
curl -s -X POST "http://localhost:3000/api/comments/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师"], "limit": 2}'
echo
echo

echo -e "${GREEN}🎉 API测试完成！${NC}"
echo
echo -e "${BLUE}💡 提示：${NC}"
echo "• 如果看到数据为空，请先导入数据："
echo "  curl -X POST http://localhost:3000/api/data/refresh"
echo "• 如果需要格式化JSON输出，请安装jq："
echo "  brew install jq (macOS) 或 apt-get install jq (Linux)"
