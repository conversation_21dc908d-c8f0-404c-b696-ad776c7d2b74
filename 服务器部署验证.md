# 服务器部署验证指南

## 📦 部署包信息

- **文件名**: `bilibili-deployment-package.tar.gz`
- **大小**: 2.0GB
- **架构**: x86_64 (amd64)
- **包含**: 完整Docker镜像 + 部署脚本 + 文档

## 🚀 服务器部署步骤

### 1. 上传部署包到服务器
```bash
# 使用scp上传（示例）
scp bilibili-deployment-package.tar.gz user@your-server:/opt/

# 或使用其他方式上传到服务器
```

### 2. 在服务器上解压
```bash
cd /opt
tar -xzf bilibili-deployment-package.tar.gz
cd bilibili-deployment-package
```

### 3. 检查部署包内容
```bash
ls -la
# 应该看到以下文件：
# - bilibili-system-offline.tar.gz  (完整系统包)
# - 快速部署.sh                     (一键部署脚本)
# - 导入数据.sh                     (数据导入脚本)
# - 停止服务.sh                     (停止服务脚本)
# - 离线部署指南.md                 (详细文档)
# - 部署说明.txt                    (简要说明)
```

### 4. 运行一键部署
```bash
# 设置执行权限（如果需要）
chmod +x *.sh

# 运行快速部署
./快速部署.sh
```

### 5. 导入数据
```bash
# 等待服务完全启动后执行
./导入数据.sh
```

## 🔍 部署验证

### 检查服务状态
```bash
# 进入系统目录
cd bilibili-system-offline

# 查看容器状态
docker-compose ps

# 应该看到两个服务都是 Up 状态：
# - elasticsearch
# - api
```

### 测试API接口
```bash
# 检查API服务状态
curl http://localhost:3000/api/data/status

# 查看数据统计
curl http://localhost:3000/api/data/stats

# 测试视频搜索
curl -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "limit": 3}'

# 测试全文搜索
curl -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "limit": 3}'
```

### 检查Elasticsearch
```bash
# 检查集群健康状态
curl http://localhost:9200/_cluster/health

# 查看索引信息
curl http://localhost:9200/_cat/indices?v
```

## 📊 预期结果

### 成功部署后应该看到：

1. **服务状态**：
   ```
   Name                    State    Ports
   elasticsearch          Up       0.0.0.0:9200->9200/tcp
   api                    Up       0.0.0.0:3000->3000/tcp
   ```

2. **数据统计**：
   ```json
   {
     "videos": 45,
     "comments": 4105,
     "elasticsearch_status": "green"
   }
   ```

3. **搜索结果**：
   - 视频搜索返回相关视频信息
   - 全文搜索返回匹配的内容
   - 评论分析返回关键词统计

## 🔧 常用管理命令

```bash
# 进入系统目录
cd bilibili-system-offline

# 查看服务日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 查看资源使用
docker stats
```

## ⚠️ 故障排除

### 如果部署失败：

1. **检查系统要求**：
   ```bash
   # 检查架构
   uname -m  # 应该是 x86_64
   
   # 检查内存
   free -h   # 应该有4GB+可用内存
   
   # 检查磁盘空间
   df -h     # 应该有10GB+可用空间
   ```

2. **检查Docker**：
   ```bash
   # 检查Docker版本
   docker --version
   docker-compose --version
   
   # 检查Docker服务
   systemctl status docker
   ```

3. **检查端口占用**：
   ```bash
   # 检查端口3000和9200是否被占用
   netstat -tlnp | grep -E ':(3000|9200)'
   ```

4. **查看详细日志**：
   ```bash
   # 查看部署日志
   docker-compose logs
   
   # 查看特定服务日志
   docker-compose logs elasticsearch
   docker-compose logs api
   ```

### 如果数据导入失败：

1. **检查网络连接**：
   ```bash
   # 测试MySQL连接（需要外网访问）
   telnet ********** 3307
   ```

2. **检查API服务**：
   ```bash
   # 确认API服务正常
   curl http://localhost:3000/api/data/status
   ```

3. **手动重试导入**：
   ```bash
   curl -X POST http://localhost:3000/api/data/refresh
   ```

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 服务器系统信息：`uname -a`
2. Docker版本：`docker --version`
3. 错误日志：`docker-compose logs`
4. 服务状态：`docker-compose ps`

## 🎯 部署成功标志

✅ 所有容器状态为 "Up"  
✅ API服务响应正常 (http://localhost:3000/api/data/status)  
✅ Elasticsearch集群健康 (http://localhost:9200/_cluster/health)  
✅ 数据导入完成 (45个视频，4105条评论)  
✅ 搜索功能正常工作  

当以上所有项目都显示正常时，说明部署成功！
