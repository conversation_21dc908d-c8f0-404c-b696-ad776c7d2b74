# 中文关键词处理解决方案

## 问题描述

在使用curl直接访问API时，中文关键词需要进行URL编码才能正确传递。例如：
- 直接使用：`curl "http://localhost:3000/api/videos/113599301292265?keywords=执业药师"` 可能无法正确处理
- 需要编码：`curl "http://localhost:3000/api/videos/113599301292265?keywords=%E6%89%A7%E4%B8%9A%E8%8D%AF%E5%B8%88"`

## 解决方案

### 方案1: GET方法 + URL编码（现有方案）

**优点：**
- 符合RESTful规范
- 可以直接在浏览器地址栏使用

**缺点：**
- 需要URL编码中文字符
- 在某些终端环境下可能出现兼容性问题

**使用方法：**
```bash
# 使用URL编码的中文关键词
curl "http://localhost:3000/api/videos/113599301292265?keywords=%E6%89%A7%E4%B8%9A%E8%8D%AF%E5%B8%88"
```

### 方案2: POST方法 + JSON请求体（推荐方案）

**优点：**
- 直接支持中文字符，无需URL编码
- 支持更复杂的参数结构
- 避免了URL长度限制
- 更好的跨平台兼容性

**缺点：**
- 不能直接在浏览器地址栏使用
- 需要设置Content-Type头

**API端点：**
```
POST /api/videos/{av}/keywords
Content-Type: application/json
```

**使用方法：**
```bash
# 单个关键词
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'

# 多个关键词（逗号分隔）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "备考,考试,诺石"}'

# 多个关键词（数组格式）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师", "备考", "考试"]}'
```

## 测试结果

### GET方法测试
```bash
# URL编码方式 - 成功
curl "http://localhost:3000/api/videos/113599301292265?keywords=%E6%89%A7%E4%B8%9A%E8%8D%AF%E5%B8%88" | jq '.data.keywordComments | length'
# 输出: 12
```

### POST方法测试
```bash
# 直接中文 - 成功
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}' | jq '.data.keywordComments | length'
# 输出: 12

# 多个关键词 - 成功
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "备考,考试"}' | jq '.data.keywordComments | length'
# 输出: 11

# 数组格式 - 成功
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师", "备考", "考试"]}' | jq '.data.keywordComments | length'
# 输出: 11
```

## 服务器日志确认

服务器正确处理中文关键词，日志显示：
```
POST请求处理的关键词: [ '执业药师' ]
POST请求处理的关键词: [ '备考', '考试' ]
POST请求处理的关键词: [ '执业药师', '备考', '考试' ]
```

## 辅助工具

### 1. 自动化测试脚本
- `test_chinese_api.sh` - GET方法测试（自动URL编码）
- `test_post_chinese.sh` - POST方法测试

### 2. 使用建议

**对于程序化调用：**
- 推荐使用POST方法，直接传递中文关键词
- 使用Node.js、Python等语言的HTTP客户端库

**对于命令行测试：**
- 使用提供的测试脚本
- 或者使用POST方法避免URL编码问题

**对于浏览器测试：**
- 可以使用GET方法，浏览器会自动处理URL编码
- 或者使用Postman等工具测试POST方法

## 技术实现

### 后端实现
1. 添加了新的POST路由：`POST /api/videos/:av/keywords`
2. 在控制器中添加了`getVideoByAvWithKeywords`方法
3. 支持JSON请求体中的关键词参数
4. 保持与GET方法相同的返回数据结构

### 中间件支持
Express应用已配置UTF-8字符编码支持：
```javascript
app.use((req, res, next) => {
  res.charset = 'utf-8';
  next();
});
```

## 总结

通过提供GET和POST两种方法，系统现在可以：
1. 兼容原有的GET方法调用
2. 提供更友好的POST方法处理中文关键词
3. 支持多种关键词格式（字符串、数组）
4. 确保跨平台兼容性

推荐在实际使用中优先选择POST方法，以获得最佳的中文字符处理体验。
