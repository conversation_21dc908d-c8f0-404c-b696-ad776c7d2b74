# Docker阿里云镜像源配置指南

## 🚀 配置步骤

### 1. 配置Docker daemon使用阿里云镜像源

根据您的操作系统，将以下配置添加到Docker配置文件：

#### macOS
```bash
# 创建或编辑配置文件
nano ~/.docker/daemon.json
```

#### Linux
```bash
# 创建或编辑配置文件
sudo nano /etc/docker/daemon.json
```

#### Windows
```bash
# 编辑配置文件
notepad %USERPROFILE%\.docker\daemon.json
```

### 2. 配置内容

将以下内容复制到 `daemon.json` 文件中：

```json
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
```

### 3. 重启Docker服务

#### macOS/Windows
- 重启Docker Desktop应用程序

#### Linux
```bash
sudo systemctl restart docker
```

### 4. 验证配置

```bash
# 查看Docker信息，确认镜像源配置
docker info | grep -A 10 "Registry Mirrors"
```

## 📋 当前项目配置

### Docker镜像源
✅ **使用阿里云镜像源**：`https://registry.cn-hangzhou.aliyuncs.com`

### 应用镜像
✅ **使用官方源**：
- **Elasticsearch**: `docker.elastic.co/elasticsearch/elasticsearch:8.11.0`
- **Kibana**: `docker.elastic.co/kibana/kibana:8.11.0`
- **Node.js**: `node:18-alpine`

### npm源
✅ **使用官方源**：`https://registry.npmjs.org`

## 🛠️ 部署命令

配置完成后，使用以下命令部署：

```bash
# 清理现有容器和卷
docker-compose down -v

# 清理构建缓存
docker system prune -f

# 重新构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🔍 故障排查

### 1. 检查Docker配置
```bash
docker info | grep -A 5 "Registry Mirrors"
```

### 2. 测试镜像拉取
```bash
docker pull hello-world
```

### 3. 检查网络连接
```bash
curl -I https://registry.cn-hangzhou.aliyuncs.com
```

### 4. 查看详细错误
```bash
docker-compose up --no-deps --build api
```

## 📊 性能对比

使用阿里云镜像源后：
- **镜像拉取速度**：显著提升（特别是在中国大陆地区）
- **构建时间**：减少网络等待时间
- **稳定性**：提高连接成功率

## ⚠️ 注意事项

1. **配置生效**：修改 `daemon.json` 后必须重启Docker服务
2. **权限问题**：Linux系统可能需要sudo权限
3. **网络环境**：确保能够访问阿里云镜像源
4. **版本兼容**：某些特殊版本可能在镜像源中不可用

## 🎯 推荐做法

1. **优先使用官方镜像**：确保版本完整性和安全性
2. **配置镜像源加速**：通过daemon.json配置全局加速
3. **定期更新**：保持Docker和镜像的最新版本
4. **监控日志**：关注构建和运行日志，及时发现问题
