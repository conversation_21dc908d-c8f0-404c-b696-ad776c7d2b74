#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 导入Bilibili数据 ===${NC}"
echo

# 进入系统目录
if [ -d "bilibili-system-offline" ]; then
    cd bilibili-system-offline
else
    echo -e "${RED}❌ 未找到系统目录，请先运行快速部署脚本${NC}"
    exit 1
fi

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务状态...${NC}"
if ! curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
    echo -e "${RED}❌ API服务未运行，请先运行快速部署脚本${NC}"
    exit 1
fi

echo -e "${GREEN}✅ API服务运行正常${NC}"

# 显示当前数据状态
echo -e "${YELLOW}📊 当前数据状态:${NC}"
curl -s http://localhost:3000/api/data/stats | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/stats

echo
echo -e "${YELLOW}📥 开始导入数据...${NC}"
echo "这可能需要几分钟时间，请耐心等待..."
echo

# 导入数据
if curl -X POST http://localhost:3000/api/data/refresh; then
    echo
    echo -e "${GREEN}🎉 数据导入完成！${NC}"
    echo
    echo -e "${BLUE}📊 最新数据统计：${NC}"
    curl -s http://localhost:3000/api/data/stats | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/stats
    echo
    echo -e "${BLUE}🧪 测试搜索功能：${NC}"
    echo '• 搜索视频: curl -X POST "http://localhost:3000/api/videos/search" -H "Content-Type: application/json" -d '"'"'{"q": "诺石医考"}'"'"
    echo '• 全文搜索: curl -X POST "http://localhost:3000/api/search" -H "Content-Type: application/json" -d '"'"'{"q": "执业药师"}'"'"
else
    echo
    echo -e "${RED}❌ 数据导入失败${NC}"
    echo -e "${YELLOW}请检查：${NC}"
    echo "1. MySQL数据库连接是否正常"
    echo "2. 网络连接是否正常"
    echo "3. 查看API服务日志: docker-compose logs api"
    exit 1
fi
