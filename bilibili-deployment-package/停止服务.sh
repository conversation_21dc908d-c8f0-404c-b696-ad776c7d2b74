#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 停止Bilibili数据检索系统 ===${NC}"
echo

# 进入系统目录
if [ -d "bilibili-system-offline" ]; then
    cd bilibili-system-offline
else
    echo -e "${RED}❌ 未找到系统目录${NC}"
    exit 1
fi

echo -e "${YELLOW}🛑 停止所有服务...${NC}"
docker-compose down

echo -e "${YELLOW}🧹 清理容器和网络...${NC}"
docker-compose down -v

echo -e "${GREEN}✅ 服务已停止${NC}"
echo
echo -e "${BLUE}💡 如需重新启动服务，请运行:${NC}"
echo "cd bilibili-system-offline && docker-compose up -d"
