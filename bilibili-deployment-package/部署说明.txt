Bilibili数据检索系统 - 部署包说明
=====================================

📦 包含文件：
- bilibili-system-offline.tar.gz  (完整系统包，2.0GB)
- 快速部署.sh                     (一键部署脚本)
- 导入数据.sh                     (数据导入脚本)
- 停止服务.sh                     (停止服务脚本)
- 离线部署指南.md                 (详细部署文档)
- 部署说明.txt                    (本文件)

🚀 快速部署步骤：
1. 上传整个部署包到服务器
2. 解压: tar -xzf bilibili-deployment-package.tar.gz
3. 进入目录: cd bilibili-deployment-package
4. 运行部署: ./快速部署.sh
5. 导入数据: ./导入数据.sh

⚠️ 系统要求：
- Linux x86_64 系统
- Docker 20.10+
- Docker Compose 1.29+
- 4GB+ 内存
- 10GB+ 存储空间
- 端口 3000, 9200 未被占用

📋 服务地址：
- API服务: http://localhost:3000
- Elasticsearch: http://localhost:9200

🔧 常用命令：
- 查看服务状态: docker-compose ps
- 查看日志: docker-compose logs -f
- 重启服务: docker-compose restart
- 停止服务: ./停止服务.sh

📚 详细说明请查看: 离线部署指南.md
