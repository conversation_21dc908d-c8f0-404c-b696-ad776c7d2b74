#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Bilibili数据检索系统 - 快速部署 ===${NC}"
echo

# 检查系统要求
echo -e "${YELLOW}🔍 检查系统要求...${NC}"

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "x86_64" ]; then
    echo -e "${RED}❌ 系统架构不匹配，需要 x86_64，当前: $ARCH${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 系统架构: $ARCH${NC}"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker已安装: $(docker --version)${NC}"

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker Compose已安装: $(docker-compose --version)${NC}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker服务${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker服务运行正常${NC}"

# 检查端口占用
echo -e "${YELLOW}🔍 检查端口占用...${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
    echo -e "${RED}❌ 端口3000已被占用${NC}"
    exit 1
fi

if netstat -tlnp 2>/dev/null | grep -q ":9200 "; then
    echo -e "${RED}❌ 端口9200已被占用${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 端口检查通过${NC}"

echo

# 解压系统包
echo -e "${YELLOW}📦 解压系统包...${NC}"
if [ ! -f "bilibili-system-offline.tar.gz" ]; then
    echo -e "${RED}❌ 未找到系统包文件${NC}"
    exit 1
fi

tar -xzf bilibili-system-offline.tar.gz
cd bilibili-system-offline

# 加载Docker镜像
echo -e "${YELLOW}🐳 加载Docker镜像...${NC}"
echo "这可能需要几分钟时间，请耐心等待..."

for tar_file in docker-images/*.tar; do
    if [ -f "$tar_file" ]; then
        echo -e "${BLUE}加载: $(basename "$tar_file")${NC}"
        if docker load -i "$tar_file"; then
            echo -e "${GREEN}✅ 加载成功${NC}"
        else
            echo -e "${RED}❌ 加载失败: $tar_file${NC}"
            exit 1
        fi
    fi
done

echo

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose down -v 2>/dev/null
docker-compose up -d

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo -e "${YELLOW}查看错误日志:${NC}"
    docker-compose logs
    exit 1
fi

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 20

# 检查Elasticsearch
echo -e "${YELLOW}🔍 检查Elasticsearch...${NC}"
for i in {1..12}; do
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待Elasticsearch启动... ($i/12)${NC}"
        sleep 10
    fi
    
    if [ $i -eq 12 ]; then
        echo -e "${RED}❌ Elasticsearch启动超时${NC}"
        echo -e "${YELLOW}查看日志:${NC}"
        docker-compose logs elasticsearch
        exit 1
    fi
done

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务...${NC}"
for i in {1..6}; do
    if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待API服务启动... ($i/6)${NC}"
        sleep 5
    fi
    
    if [ $i -eq 6 ]; then
        echo -e "${RED}❌ API服务启动超时${NC}"
        echo -e "${YELLOW}查看日志:${NC}"
        docker-compose logs api
        exit 1
    fi
done

echo
echo -e "${GREEN}🎉 部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务地址：${NC}"
echo "• API服务: http://localhost:3000"
echo "• Elasticsearch: http://localhost:9200"
echo
echo -e "${BLUE}🔧 下一步操作：${NC}"
echo "1. 导入数据:"
echo "   curl -X POST http://localhost:3000/api/data/refresh"
echo
echo "2. 测试API:"
echo '   curl -X POST "http://localhost:3000/api/videos/search" -H "Content-Type: application/json" -d '"'"'{"q": "诺石医考"}'"'"
echo
echo "3. 查看服务状态:"
echo "   docker-compose ps"
echo
echo "4. 查看日志:"
echo "   docker-compose logs -f"
echo
echo -e "${YELLOW}💡 提示：首次使用需要导入数据，这可能需要几分钟时间${NC}"
