const SearchService = require('./src/services/SearchService');

async function debugKeywordMatching() {
  console.log('=== 调试关键词匹配逻辑 ===\n');
  
  const searchService = new SearchService();
  const avNumber = '113599301292265';
  const keywords = ['执业药师', '备考'];
  
  try {
    // 1. 获取最新评论
    console.log('1. 获取最新3条评论:');
    const latestComments = await searchService.getLatestComments(avNumber, 3);
    
    latestComments.forEach((comment, index) => {
      console.log(`   评论${index + 1}:`);
      console.log(`   - ID: ${comment.comment_id}`);
      console.log(`   - 内容: ${comment.content.substring(0, 50)}...`);
      console.log(`   - 时间: ${comment.create_date}`);
      
      // 检查是否包含关键词
      const hasKeywords = keywords.some(keyword => 
        comment.content.toLowerCase().includes(keyword.toLowerCase())
      );
      console.log(`   - 包含关键词: ${hasKeywords ? '是' : '否'}`);
      
      if (hasKeywords) {
        const matchedKeywords = keywords.filter(keyword => 
          comment.content.toLowerCase().includes(keyword.toLowerCase())
        );
        console.log(`   - 匹配的关键词: ${matchedKeywords.join(', ')}`);
      }
      console.log('');
    });
    
    // 2. 获取关键词评论
    console.log('\n2. 获取包含关键词的评论:');
    const keywordComments = await searchService.getKeywordComments(avNumber, keywords, 5);
    
    console.log(`   找到 ${keywordComments.length} 条包含关键词的评论\n`);
    
    keywordComments.slice(0, 5).forEach((comment, index) => {
      console.log(`   关键词评论${index + 1}:`);
      console.log(`   - ID: ${comment.comment_id}`);
      console.log(`   - 内容: ${comment.content.substring(0, 50)}...`);
      console.log(`   - 时间: ${comment.create_date}`);
      console.log(`   - 是否回复: ${comment.is_reply ? '是' : '否'}`);
      console.log('');
    });
    
    // 3. 交叉检查：最新评论中包含关键词的是否都出现在关键词评论中
    console.log('\n3. 交叉检查:');
    const latestWithKeywords = latestComments.filter(comment => 
      keywords.some(keyword => 
        comment.content.toLowerCase().includes(keyword.toLowerCase())
      )
    );
    
    console.log(`   最新评论中包含关键词的数量: ${latestWithKeywords.length}`);
    
    latestWithKeywords.forEach(latestComment => {
      const foundInKeywordComments = keywordComments.some(keywordComment => 
        keywordComment.comment_id === latestComment.comment_id
      );
      
      console.log(`   - 评论ID ${latestComment.comment_id}: ${foundInKeywordComments ? '✅ 在关键词评论中找到' : '❌ 在关键词评论中未找到'}`);
      
      if (!foundInKeywordComments) {
        console.log(`     内容: ${latestComment.content}`);
        console.log(`     父评论ID: ${latestComment.parent_comment_id}`);
      }
    });
    
    // 4. 检查评论组织逻辑
    console.log('\n4. 检查评论组织逻辑:');
    const allComments = await searchService.searchCommentsByAv(avNumber, {
      limit: 100,
      sortBy: 'create_time',
      sortOrder: 'desc'
    });
    
    console.log(`   总评论数: ${allComments.total}`);
    console.log(`   获取到的评论数: ${allComments.comments.length}`);
    
    // 手动检查评论组织
    const commentGroups = searchService.organizeCommentGroups(allComments.comments);
    console.log(`   组织后的评论组数: ${commentGroups.length}`);
    
    // 检查包含关键词的组
    const keywordGroups = commentGroups.filter(group =>
      searchService.groupHasKeywords(group, keywords)
    );
    console.log(`   包含关键词的评论组数: ${keywordGroups.length}`);
    
  } catch (error) {
    console.error('调试过程中出错:', error);
  }
}

// 运行调试
debugKeywordMatching();
