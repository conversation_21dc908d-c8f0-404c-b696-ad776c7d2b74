const { elasticsearchClient } = require('../config/database');
const config = require('../config/app');

class SearchService {
  constructor() {
    this.videoIndex = config.elasticsearch.indices.video;
    this.commentIndex = config.elasticsearch.indices.comment;
  }

  // 根据AV号搜索视频
  async searchVideoByAv(avNumber) {
    try {
      const response = await elasticsearchClient.search({
        index: this.videoIndex,
        body: {
          query: {
            bool: {
              should: [
                { term: { av_number: avNumber } },
                { term: { video_id: avNumber } },
                { wildcard: { video_url: `*av${avNumber}*` } }
              ],
              minimum_should_match: 1
            }
          }
        }
      });

      return response.hits.hits.map(hit => ({
        ...hit._source,
        _id: hit._id,
        _score: hit._score
      }));
    } catch (error) {
      console.error('搜索视频失败:', error);
      throw error;
    }
  }

  // 根据AV号搜索评论
  async searchCommentsByAv(avNumber, options = {}) {
    const {
      limit = 20,
      offset = 0,
      sortBy = 'create_time',
      sortOrder = 'desc',
      hasKeywords = false
    } = options;

    try {
      const query = {
        bool: {
          must: [
            {
              bool: {
                should: [
                  { term: { av_number: avNumber } },
                  { term: { video_id: avNumber } }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      };

      // 如果需要筛选包含关键词的评论
      if (hasKeywords) {
        query.bool.must.push({
          exists: { field: 'keywords' }
        });
        query.bool.must.push({
          bool: {
            must_not: [
              { term: { 'keywords.keyword': '' } }
            ]
          }
        });
      }

      const response = await elasticsearchClient.search({
        index: this.commentIndex,
        body: {
          query,
          sort: [
            { [sortBy]: { order: sortOrder } }
          ],
          from: offset,
          size: limit
        }
      });

      return {
        total: response.hits.total.value,
        comments: response.hits.hits.map(hit => ({
          ...hit._source,
          _id: hit._id,
          _score: hit._score
        }))
      };
    } catch (error) {
      console.error('搜索评论失败:', error);
      throw error;
    }
  }

  // 获取最新评论
  async getLatestComments(avNumber, limit = 3) {
    try {
      const result = await this.searchCommentsByAv(avNumber, {
        limit,
        sortBy: 'create_time',
        sortOrder: 'desc'
      });

      return result.comments;
    } catch (error) {
      console.error('获取最新评论失败:', error);
      throw error;
    }
  }

  // 获取包含关键词的评论组（主评论+回复）
  async getKeywordComments(avNumber, keywords = [], limit = 10) {
    try {
      // 如果没有提供关键词，返回空数组
      if (!keywords || keywords.length === 0) {
        return [];
      }

      // 首先获取所有评论
      const allComments = await this.searchCommentsByAv(avNumber, {
        limit: 1000, // 减少评论数量以提高性能
        sortBy: 'create_time',
        sortOrder: 'desc'
      });

      if (allComments.total === 0) return [];

      // 按评论组织结构
      const commentGroups = this.organizeCommentGroups(allComments.comments);

      // 筛选包含关键词的评论组
      const keywordGroups = commentGroups.filter(group =>
        this.groupHasKeywords(group, keywords)
      );

      // 按主评论时间排序并限制数量
      keywordGroups.sort((a, b) => b.mainComment.create_time - a.mainComment.create_time);

      // 返回扁平化的评论列表，但保持组结构
      const result = [];
      for (let i = 0; i < Math.min(keywordGroups.length, limit); i++) {
        const group = keywordGroups[i];
        result.push(group.mainComment);
        result.push(...group.replies);
      }

      return result;
    } catch (error) {
      console.error('获取关键词评论失败:', error);
      throw error;
    }
  }

  // 组织评论结构（主评论+回复）
  organizeCommentGroups(comments) {
    const mainComments = new Map();
    const replies = new Map();

    // 分离主评论和回复
    comments.forEach(comment => {
      if (comment.parent_comment_id === '0' || !comment.parent_comment_id) {
        mainComments.set(comment.comment_id, {
          mainComment: comment,
          replies: []
        });
      } else {
        if (!replies.has(comment.parent_comment_id)) {
          replies.set(comment.parent_comment_id, []);
        }
        replies.get(comment.parent_comment_id).push(comment);
      }
    });

    // 将回复关联到主评论
    replies.forEach((replyList, parentId) => {
      if (mainComments.has(parentId)) {
        mainComments.get(parentId).replies = replyList.sort((a, b) =>
          a.create_time - b.create_time // 回复按时间正序
        );
      }
    });

    return Array.from(mainComments.values());
  }

  // 检查评论组是否包含关键词
  groupHasKeywords(group, keywords) {
    // 检查主评论是否包含任何关键词
    if (this.contentHasKeywords(group.mainComment.content, keywords)) {
      return true;
    }

    // 检查任何回复是否包含关键词
    return group.replies.some(reply =>
      this.contentHasKeywords(reply.content, keywords)
    );
  }

  // 检查内容是否包含关键词
  contentHasKeywords(content, keywords) {
    if (!content || !keywords || keywords.length === 0) return false;

    const lowerContent = content.toLowerCase();
    return keywords.some(keyword =>
      lowerContent.includes(keyword.toLowerCase())
    );
  }

  // 计算评论组相似度重复率（简化版本）
  async calculateCommentDuplicateRate(avNumber) {
    try {
      const allComments = await this.searchCommentsByAv(avNumber, {
        limit: 1000 // 限制评论数量，避免计算过慢
      });

      if (allComments.total === 0) return 0;

      // 组织评论组
      const commentGroups = this.organizeCommentGroups(allComments.comments);

      if (commentGroups.length < 2) return 0;

      // 简化计算：只比较主评论内容的相似度
      const leven = require('leven');
      let similarGroupCount = 0;
      const threshold = 0.7; // 降低相似度阈值

      // 限制比较数量，避免超时
      const maxComparisons = Math.min(commentGroups.length, 20);

      for (let i = 0; i < maxComparisons; i++) {
        for (let j = i + 1; j < maxComparisons; j++) {
          const content1 = commentGroups[i].mainComment.content.trim().toLowerCase();
          const content2 = commentGroups[j].mainComment.content.trim().toLowerCase();

          if (content1.length === 0 || content2.length === 0) continue;

          const distance = leven(content1, content2);
          const maxLength = Math.max(content1.length, content2.length);
          const similarity = 1 - (distance / maxLength);

          if (similarity >= threshold) {
            similarGroupCount++;
            break;
          }
        }
      }

      return (similarGroupCount / Math.min(commentGroups.length, maxComparisons) * 100).toFixed(1);
    } catch (error) {
      console.error('计算重复率失败:', error);
      return 0;
    }
  }



  // 获取详细的评论统计信息
  async getDetailedCommentStats(avNumber) {
    try {
      const response = await elasticsearchClient.search({
        index: this.commentIndex,
        body: {
          query: {
            bool: {
              should: [
                { term: { av_number: avNumber } },
                { term: { video_id: avNumber } }
              ],
              minimum_should_match: 1
            }
          },
          aggs: {
            // 点赞数统计
            like_stats: {
              stats: {
                field: 'like_count'
              }
            },
            // 评论长度统计
            content_length_stats: {
              stats: {
                field: 'content_length'
              }
            },
            // 回复评论统计
            reply_stats: {
              terms: {
                field: 'is_reply'
              }
            },
            // 关键词统计
            keyword_stats: {
              terms: {
                field: 'keywords.keyword',
                size: 10
              }
            },
            // 用户活跃度统计
            user_stats: {
              cardinality: {
                field: 'user_id.keyword'
              }
            }
          },
          size: 0
        }
      });

      const aggs = response.aggregations;

      return {
        total: response.hits.total.value,
        uniqueUsers: aggs.user_stats.value,
        likeStats: {
          avg: Math.round(aggs.like_stats.avg || 0),
          max: aggs.like_stats.max || 0,
          min: aggs.like_stats.min || 0,
          sum: aggs.like_stats.sum || 0
        },
        contentLengthStats: {
          avg: Math.round(aggs.content_length_stats.avg || 0),
          max: aggs.content_length_stats.max || 0,
          min: aggs.content_length_stats.min || 0
        },
        replyStats: {
          replies: aggs.reply_stats.buckets.find(b => b.key === true)?.doc_count || 0,
          directComments: aggs.reply_stats.buckets.find(b => b.key === false)?.doc_count || 0
        },
        keywordStats: aggs.keyword_stats.buckets.map(bucket => ({
          keyword: bucket.key,
          count: bucket.doc_count
        }))
      };
    } catch (error) {
      console.error('获取详细评论统计失败:', error);
      return null;
    }
  }

  // 综合搜索 - 根据AV号获取完整信息
  async getVideoWithComments(avNumber, keywords = []) {
    try {

      // 获取视频信息
      const videos = await this.searchVideoByAv(avNumber);
      if (videos.length === 0) {
        return null;
      }

      const video = videos[0];

      // 获取最新评论（不区分一级二级）
      const latestComments = await this.getLatestComments(avNumber, 3);

      // 获取关键词评论组（限制5组，避免返回数据过多）
      const keywordComments = await this.getKeywordComments(avNumber, keywords, 5);

      // 计算重复率
      const duplicateRate = await this.calculateCommentDuplicateRate(avNumber);

      // 获取详细评论统计
      const detailedStats = await this.getDetailedCommentStats(avNumber);

      // 计算视频热度指数（简单算法）
      const hotIndex = this.calculateHotIndex(video, detailedStats);

      return {
        video: {
          ...video,
          // 增加计算字段
          engagement_rate: this.calculateEngagementRate(video),
          hot_index: hotIndex
        },
        stats: {
          totalComments: detailedStats?.total || 0,
          duplicateRate: parseFloat(duplicateRate),
          keywordCommentGroups: this.countKeywordGroups(keywordComments),
          ...detailedStats
        },
        latestComments,
        keywordComments
      };
    } catch (error) {
      console.error('综合搜索失败:', error);
      throw error;
    }
  }

  // 计算参与度（评论数/播放量）
  calculateEngagementRate(video) {
    if (!video.video_play_count || video.video_play_count === 0) return 0;
    return ((video.video_comment / video.video_play_count) * 100).toFixed(2);
  }

  // 计算热度指数
  calculateHotIndex(video, commentStats) {
    const playCount = video.video_play_count || 0;
    const likeCount = video.liked_count || 0;
    const commentCount = commentStats?.total || 0;
    const uniqueUsers = commentStats?.uniqueUsers || 0;

    // 简单的热度计算公式
    const hotIndex = Math.round(
      (playCount * 0.1) +
      (likeCount * 2) +
      (commentCount * 5) +
      (uniqueUsers * 3)
    );

    return hotIndex;
  }

  // 全文搜索
  async fullTextSearch(query, options = {}) {
    const {
      limit = 20,
      offset = 0,
      type = 'all' // 'video', 'comment', 'all'
    } = options;

    try {
      const searchBody = {
        query: {
          multi_match: {
            query,
            fields: ['title^2', 'desc', 'content', 'nickname'],
            type: 'best_fields'
          }
        },
        from: offset,
        size: limit
      };

      let results = {};

      if (type === 'all' || type === 'video') {
        const videoResponse = await elasticsearchClient.search({
          index: this.videoIndex,
          body: searchBody
        });

        results.videos = {
          total: videoResponse.hits.total.value,
          items: videoResponse.hits.hits.map(hit => ({
            ...hit._source,
            _id: hit._id,
            _score: hit._score
          }))
        };
      }

      if (type === 'all' || type === 'comment') {
        const commentResponse = await elasticsearchClient.search({
          index: this.commentIndex,
          body: searchBody
        });

        results.comments = {
          total: commentResponse.hits.total.value,
          items: commentResponse.hits.hits.map(hit => ({
            ...hit._source,
            _id: hit._id,
            _score: hit._score
          }))
        };
      }

      return results;
    } catch (error) {
      console.error('全文搜索失败:', error);
      throw error;
    }
  }

  // 计算关键词评论组数量
  countKeywordGroups(keywordComments) {
    if (!keywordComments || keywordComments.length === 0) return 0;

    let groupCount = 0;

    keywordComments.forEach(comment => {
      if (comment.parent_comment_id === '0' || !comment.parent_comment_id) {
        groupCount++;
      }
    });

    return groupCount;
  }
}

module.exports = SearchService;
