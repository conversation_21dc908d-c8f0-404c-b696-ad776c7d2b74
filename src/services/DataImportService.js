const { mysqlPool, elasticsearchClient } = require('../config/database');
const config = require('../config/app');
const Video = require('../models/Video');
const Comment = require('../models/Comment');

class DataImportService {
  constructor() {
    this.batchSize = 1000;
    this.videoIndex = config.elasticsearch.indices.video;
    this.commentIndex = config.elasticsearch.indices.comment;
  }

  // 从 MySQL 获取视频数据
  async getVideosFromMySQL(limit = null, offset = 0) {
    try {
      let query = 'SELECT * FROM bilibili_video';

      if (limit) {
        query += ` LIMIT ${limit} OFFSET ${offset}`;
      }

      const [rows] = await mysqlPool.execute(query);
      return rows.map(row => new Video(row));
    } catch (error) {
      console.error('获取视频数据失败:', error);
      throw error;
    }
  }

  // 从 MySQL 获取评论数据
  async getCommentsFromMySQL(limit = null, offset = 0) {
    try {
      let query = 'SELECT * FROM bilibili_video_comment';

      if (limit) {
        query += ` LIMIT ${limit} OFFSET ${offset}`;
      }

      const [rows] = await mysqlPool.execute(query);
      return rows.map(row => new Comment(row));
    } catch (error) {
      console.error('获取评论数据失败:', error);
      throw error;
    }
  }

  // 获取数据总数
  async getDataCounts() {
    try {
      const [videoRows] = await mysqlPool.execute('SELECT COUNT(*) as count FROM bilibili_video');
      const [commentRows] = await mysqlPool.execute('SELECT COUNT(*) as count FROM bilibili_video_comment');
      
      return {
        videos: videoRows[0].count,
        comments: commentRows[0].count
      };
    } catch (error) {
      console.error('获取数据总数失败:', error);
      throw error;
    }
  }

  // 批量导入视频到 Elasticsearch
  async importVideosToElasticsearch(videos) {
    if (!videos || videos.length === 0) return { success: 0, failed: 0 };

    const body = [];
    let validVideos = 0;

    videos.forEach(video => {
      if (video.isValid()) {
        body.push({
          index: {
            _index: this.videoIndex,
            _id: video.id
          }
        });
        body.push(video.toElasticsearchDoc());
        validVideos++;
      }
    });

    if (body.length === 0) {
      return { success: 0, failed: 0 };
    }

    try {
      const response = await elasticsearchClient.bulk({ body });
      
      let success = 0;
      let failed = 0;
      
      if (response.items) {
        response.items.forEach(item => {
          if (item.index && item.index.status < 300) {
            success++;
          } else {
            failed++;
          }
        });
      }

      return { success, failed, total: validVideos };
    } catch (error) {
      console.error('批量导入视频失败:', error);
      throw error;
    }
  }

  // 批量导入评论到 Elasticsearch
  async importCommentsToElasticsearch(comments) {
    if (!comments || comments.length === 0) return { success: 0, failed: 0 };

    const body = [];
    let validComments = 0;

    comments.forEach(comment => {
      if (comment.isValid()) {
        body.push({
          index: {
            _index: this.commentIndex,
            _id: comment.id
          }
        });
        body.push(comment.toElasticsearchDoc());
        validComments++;
      }
    });

    if (body.length === 0) {
      return { success: 0, failed: 0 };
    }

    try {
      const response = await elasticsearchClient.bulk({ body });
      
      let success = 0;
      let failed = 0;
      
      if (response.items) {
        response.items.forEach(item => {
          if (item.index && item.index.status < 300) {
            success++;
          } else {
            failed++;
          }
        });
      }

      return { success, failed, total: validComments };
    } catch (error) {
      console.error('批量导入评论失败:', error);
      throw error;
    }
  }

  // 完整的数据导入流程
  async importAllData() {
    console.log('🚀 开始数据导入流程...');
    
    try {
      // 获取数据总数
      const counts = await this.getDataCounts();
      console.log(`📊 数据统计: 视频 ${counts.videos} 条, 评论 ${counts.comments} 条`);

      // 导入视频数据
      console.log('📹 开始导入视频数据...');
      let videoOffset = 0;
      let totalVideoSuccess = 0;
      let totalVideoFailed = 0;

      while (videoOffset < counts.videos) {
        const videos = await this.getVideosFromMySQL(this.batchSize, videoOffset);
        if (videos.length === 0) break;

        const result = await this.importVideosToElasticsearch(videos);
        totalVideoSuccess += result.success;
        totalVideoFailed += result.failed;

        console.log(`📹 视频批次 ${Math.floor(videoOffset / this.batchSize) + 1}: 成功 ${result.success}, 失败 ${result.failed}`);
        
        videoOffset += this.batchSize;
      }

      // 导入评论数据
      console.log('💬 开始导入评论数据...');
      let commentOffset = 0;
      let totalCommentSuccess = 0;
      let totalCommentFailed = 0;

      while (commentOffset < counts.comments) {
        const comments = await this.getCommentsFromMySQL(this.batchSize, commentOffset);
        if (comments.length === 0) break;

        const result = await this.importCommentsToElasticsearch(comments);
        totalCommentSuccess += result.success;
        totalCommentFailed += result.failed;

        console.log(`💬 评论批次 ${Math.floor(commentOffset / this.batchSize) + 1}: 成功 ${result.success}, 失败 ${result.failed}`);
        
        commentOffset += this.batchSize;
      }

      console.log('✅ 数据导入完成!');
      console.log(`📊 最终统计:`);
      console.log(`   视频: 成功 ${totalVideoSuccess}, 失败 ${totalVideoFailed}`);
      console.log(`   评论: 成功 ${totalCommentSuccess}, 失败 ${totalCommentFailed}`);

      return {
        videos: { success: totalVideoSuccess, failed: totalVideoFailed },
        comments: { success: totalCommentSuccess, failed: totalCommentFailed }
      };

    } catch (error) {
      console.error('❌ 数据导入失败:', error);
      throw error;
    }
  }
}

module.exports = DataImportService;
