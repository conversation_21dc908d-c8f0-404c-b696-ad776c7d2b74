const { mysqlPool, elasticsearchClient } = require('../config/database');
const config = require('../config/app');
const Video = require('../models/Video');
const Comment = require('../models/Comment');

class OptimizedDataImportService {
  constructor() {
    this.batchSize = 1000;
    this.videoIndex = config.elasticsearch.indices.video;
    this.commentIndex = config.elasticsearch.indices.comment;
    this.videoGroupIndex = 'bilibili_video_groups'; // 新的分组索引
  }

  // 按视频ID分组获取评论数据
  async getCommentsGroupedByVideo() {
    try {
      const query = `
        SELECT 
          video_id,
          COUNT(*) as comment_count,
          GROUP_CONCAT(
            JSON_OBJECT(
              'id', id,
              'user_id', user_id,
              'nickname', nickname,
              'avatar', avatar,
              'comment_id', comment_id,
              'content', content,
              'create_time', create_time,
              'parent_comment_id', parent_comment_id,
              'like_count', like_count,
              'sub_comment_count', sub_comment_count
            )
          ) as comments_data
        FROM bilibili_video_comment 
        GROUP BY video_id
        ORDER BY video_id
      `;

      const [rows] = await mysqlPool.execute(query);
      
      return rows.map(row => ({
        video_id: row.video_id,
        comment_count: row.comment_count,
        comments: JSON.parse(`[${row.comments_data}]`)
      }));
    } catch (error) {
      console.error('按视频ID分组获取评论失败:', error);
      throw error;
    }
  }

  // 创建视频分组索引的映射
  async createVideoGroupIndex() {
    try {
      const indexExists = await elasticsearchClient.indices.exists({
        index: this.videoGroupIndex
      });

      if (indexExists.body) {
        console.log(`索引 ${this.videoGroupIndex} 已存在`);
        return;
      }

      await elasticsearchClient.indices.create({
        index: this.videoGroupIndex,
        body: {
          settings: {
            number_of_shards: 1,
            number_of_replicas: 0,
            analysis: {
              analyzer: {
                chinese_analyzer: {
                  type: 'custom',
                  tokenizer: 'ik_max_word',
                  filter: ['lowercase']
                }
              }
            }
          },
          mappings: {
            properties: {
              video_id: {
                type: 'keyword'
              },
              av_number: {
                type: 'keyword'
              },
              comment_count: {
                type: 'integer'
              },
              main_comments: {
                type: 'nested',
                properties: {
                  comment_id: { type: 'keyword' },
                  user_id: { type: 'keyword' },
                  nickname: { type: 'text', analyzer: 'chinese_analyzer' },
                  content: { type: 'text', analyzer: 'chinese_analyzer' },
                  create_time: { type: 'long' },
                  create_date: { type: 'date' },
                  like_count: { type: 'integer' },
                  sub_comment_count: { type: 'integer' },
                  replies: {
                    type: 'nested',
                    properties: {
                      comment_id: { type: 'keyword' },
                      user_id: { type: 'keyword' },
                      nickname: { type: 'text', analyzer: 'chinese_analyzer' },
                      content: { type: 'text', analyzer: 'chinese_analyzer' },
                      create_time: { type: 'long' },
                      create_date: { type: 'date' },
                      like_count: { type: 'integer' },
                      parent_comment_id: { type: 'keyword' }
                    }
                  }
                }
              },
              indexed_at: {
                type: 'date'
              }
            }
          }
        }
      });

      console.log(`视频分组索引 ${this.videoGroupIndex} 创建成功`);
    } catch (error) {
      console.error('创建视频分组索引失败:', error);
      throw error;
    }
  }

  // 组织评论为分组结构
  organizeCommentsForVideo(comments) {
    const mainComments = new Map();
    const replies = new Map();

    // 分离主评论和回复
    comments.forEach(comment => {
      if (comment.parent_comment_id === '0' || !comment.parent_comment_id) {
        mainComments.set(comment.comment_id, {
          comment_id: comment.comment_id,
          user_id: comment.user_id,
          nickname: comment.nickname,
          content: comment.content,
          create_time: parseInt(comment.create_time),
          create_date: this.formatTimestamp(comment.create_time),
          like_count: parseInt(comment.like_count) || 0,
          sub_comment_count: parseInt(comment.sub_comment_count) || 0,
          replies: []
        });
      } else {
        if (!replies.has(comment.parent_comment_id)) {
          replies.set(comment.parent_comment_id, []);
        }
        replies.get(comment.parent_comment_id).push({
          comment_id: comment.comment_id,
          user_id: comment.user_id,
          nickname: comment.nickname,
          content: comment.content,
          create_time: parseInt(comment.create_time),
          create_date: this.formatTimestamp(comment.create_time),
          like_count: parseInt(comment.like_count) || 0,
          parent_comment_id: comment.parent_comment_id
        });
      }
    });

    // 将回复关联到主评论
    replies.forEach((replyList, parentId) => {
      if (mainComments.has(parentId)) {
        mainComments.get(parentId).replies = replyList.sort((a, b) =>
          a.create_time - b.create_time
        );
      }
    });

    return Array.from(mainComments.values()).sort((a, b) =>
      b.create_time - a.create_time
    );
  }

  // 格式化时间戳
  formatTimestamp(timestamp) {
    if (!timestamp) return null;
    const ts = parseInt(timestamp);
    if (isNaN(ts)) return null;
    return new Date(ts * 1000).toISOString();
  }

  // 导入分组数据到Elasticsearch
  async importVideoGroupsToElasticsearch(videoGroups) {
    if (!videoGroups || videoGroups.length === 0) {
      return { success: 0, failed: 0 };
    }

    const body = [];
    let validGroups = 0;

    videoGroups.forEach(group => {
      if (group.video_id && group.comments && group.comments.length > 0) {
        const organizedComments = this.organizeCommentsForVideo(group.comments);
        
        body.push({
          index: {
            _index: this.videoGroupIndex,
            _id: group.video_id
          }
        });
        
        body.push({
          video_id: group.video_id,
          av_number: group.video_id.toString(),
          comment_count: group.comment_count,
          main_comments: organizedComments,
          indexed_at: new Date().toISOString()
        });
        
        validGroups++;
      }
    });

    if (body.length === 0) {
      return { success: 0, failed: 0 };
    }

    try {
      const response = await elasticsearchClient.bulk({ body });
      
      let success = 0;
      let failed = 0;
      
      if (response.items) {
        response.items.forEach(item => {
          if (item.index && item.index.status < 300) {
            success++;
          } else {
            failed++;
            console.error('导入失败:', item.index?.error);
          }
        });
      }

      return { success, failed, total: validGroups };
    } catch (error) {
      console.error('批量导入视频分组失败:', error);
      throw error;
    }
  }

  // 执行完整的优化导入流程
  async executeOptimizedImport() {
    try {
      console.log('开始优化导入流程...');
      
      // 1. 创建分组索引
      await this.createVideoGroupIndex();
      
      // 2. 按视频ID分组获取数据
      console.log('按视频ID分组获取评论数据...');
      const videoGroups = await this.getCommentsGroupedByVideo();
      console.log(`获取到 ${videoGroups.length} 个视频的评论分组`);
      
      // 3. 批量导入分组数据
      console.log('导入分组数据到Elasticsearch...');
      const result = await this.importVideoGroupsToElasticsearch(videoGroups);
      
      console.log('优化导入完成:', result);
      return result;
    } catch (error) {
      console.error('优化导入失败:', error);
      throw error;
    }
  }
}

module.exports = OptimizedDataImportService;
