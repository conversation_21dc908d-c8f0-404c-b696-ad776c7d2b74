const { elasticsearchClient } = require('../config/database');
const config = require('../config/app');

class ElasticsearchIndexService {
  constructor() {
    this.videoIndex = config.elasticsearch.indices.video;
    this.commentIndex = config.elasticsearch.indices.comment;
  }

  // 视频索引映射配置
  getVideoMapping() {
    return {
      mappings: {
        properties: {
          id: { type: 'keyword' },
          user_id: { type: 'keyword' },
          nickname: { 
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          avatar: { type: 'keyword', index: false },
          add_ts: { type: 'long' },
          last_modify_ts: { type: 'long' },
          video_id: { type: 'keyword' },
          av_number: { type: 'keyword' },
          video_type: { type: 'keyword' },
          title: {
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          desc: {
            type: 'text',
            analyzer: 'standard'
          },
          create_time: { type: 'long' },
          create_date: { type: 'date' },
          liked_count: { type: 'integer' },
          video_play_count: { type: 'integer' },
          video_danmaku: { type: 'integer' },
          video_comment: { type: 'integer' },
          video_url: { type: 'keyword', index: false },
          video_cover_url: { type: 'keyword', index: false },
          source_keyword: {
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          indexed_at: { type: 'date' }
        }
      },
      settings: {
        number_of_shards: 1,
        number_of_replicas: 0,
        analysis: {
          analyzer: {
            chinese_analyzer: {
              type: 'standard'
            }
          }
        }
      }
    };
  }

  // 评论索引映射配置
  getCommentMapping() {
    return {
      mappings: {
        properties: {
          id: { type: 'keyword' },
          user_id: { type: 'keyword' },
          nickname: {
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          avatar: { type: 'keyword', index: false },
          add_ts: { type: 'long' },
          last_modify_ts: { type: 'long' },
          comment_id: { type: 'keyword' },
          video_id: { type: 'keyword' },
          av_number: { type: 'keyword' },
          content: {
            type: 'text',
            analyzer: 'standard'
          },
          content_length: { type: 'integer' },
          create_time: { type: 'long' },
          create_date: { type: 'date' },
          sub_comment_count: { type: 'integer' },
          parent_comment_id: { type: 'keyword' },
          like_count: { type: 'integer' },
          is_reply: { type: 'boolean' },
          keywords: { type: 'keyword' },
          indexed_at: { type: 'date' }
        }
      },
      settings: {
        number_of_shards: 1,
        number_of_replicas: 0,
        analysis: {
          analyzer: {
            chinese_analyzer: {
              type: 'standard'
            }
          }
        }
      }
    };
  }

  // 检查索引是否存在
  async indexExists(indexName) {
    try {
      const response = await elasticsearchClient.indices.exists({
        index: indexName
      });
      return response;
    } catch (error) {
      console.error(`检查索引 ${indexName} 是否存在失败:`, error);
      return false;
    }
  }

  // 创建视频索引
  async createVideoIndex() {
    try {
      const exists = await this.indexExists(this.videoIndex);
      
      if (exists) {
        console.log(`📹 视频索引 ${this.videoIndex} 已存在`);
        return true;
      }

      await elasticsearchClient.indices.create({
        index: this.videoIndex,
        body: this.getVideoMapping()
      });

      console.log(`✅ 视频索引 ${this.videoIndex} 创建成功`);
      return true;
    } catch (error) {
      console.error(`❌ 创建视频索引失败:`, error);
      throw error;
    }
  }

  // 创建评论索引
  async createCommentIndex() {
    try {
      const exists = await this.indexExists(this.commentIndex);
      
      if (exists) {
        console.log(`💬 评论索引 ${this.commentIndex} 已存在`);
        return true;
      }

      await elasticsearchClient.indices.create({
        index: this.commentIndex,
        body: this.getCommentMapping()
      });

      console.log(`✅ 评论索引 ${this.commentIndex} 创建成功`);
      return true;
    } catch (error) {
      console.error(`❌ 创建评论索引失败:`, error);
      throw error;
    }
  }

  // 删除索引
  async deleteIndex(indexName) {
    try {
      const exists = await this.indexExists(indexName);
      
      if (!exists) {
        console.log(`索引 ${indexName} 不存在，无需删除`);
        return true;
      }

      await elasticsearchClient.indices.delete({
        index: indexName
      });

      console.log(`🗑️ 索引 ${indexName} 删除成功`);
      return true;
    } catch (error) {
      console.error(`❌ 删除索引 ${indexName} 失败:`, error);
      throw error;
    }
  }

  // 初始化所有索引
  async initializeAllIndices() {
    console.log('🔧 开始初始化 Elasticsearch 索引...');
    
    try {
      await this.createVideoIndex();
      await this.createCommentIndex();
      
      console.log('✅ 所有索引初始化完成');
      return true;
    } catch (error) {
      console.error('❌ 索引初始化失败:', error);
      throw error;
    }
  }

  // 重建所有索引
  async rebuildAllIndices() {
    console.log('🔄 开始重建所有索引...');
    
    try {
      // 删除现有索引
      await this.deleteIndex(this.videoIndex);
      await this.deleteIndex(this.commentIndex);
      
      // 重新创建索引
      await this.createVideoIndex();
      await this.createCommentIndex();
      
      console.log('✅ 所有索引重建完成');
      return true;
    } catch (error) {
      console.error('❌ 索引重建失败:', error);
      throw error;
    }
  }

  // 获取索引统计信息
  async getIndexStats() {
    try {
      const videoStats = await this.indexExists(this.videoIndex) 
        ? await elasticsearchClient.count({ index: this.videoIndex })
        : { count: 0 };
        
      const commentStats = await this.indexExists(this.commentIndex)
        ? await elasticsearchClient.count({ index: this.commentIndex })
        : { count: 0 };

      return {
        videos: videoStats.count,
        comments: commentStats.count
      };
    } catch (error) {
      console.error('获取索引统计信息失败:', error);
      return { videos: 0, comments: 0 };
    }
  }
}

module.exports = ElasticsearchIndexService;
