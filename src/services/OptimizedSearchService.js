const { elasticsearchClient, mysqlClient } = require('../config/database');
const config = require('../config/app');

class OptimizedSearchService {
  constructor() {
    this.client = elasticsearchClient;
    this.mysqlClient = mysqlClient;
    this.videoGroupIndex = 'bilibili_video_groups';
    this.commentIndex = config.elasticsearch.indices.comment;
  }

  // 基于分组索引的关键词搜索（超快速）
  async searchKeywordCommentsOptimized(avNumber, keywords = [], limit = 10) {
    try {
      if (!keywords || keywords.length === 0) {
        return [];
      }

      console.log(`使用优化搜索: 视频${avNumber}, 关键词: ${keywords.join(', ')}`);

      // 直接从分组索引中获取指定视频的所有评论
      const result = await this.client.get({
        index: this.videoGroupIndex,
        id: avNumber
      });

      if (!result.body.found) {
        console.log(`视频 ${avNumber} 的分组数据未找到`);
        return [];
      }

      const videoData = result.body._source;
      const mainComments = videoData.main_comments || [];

      console.log(`从分组索引获取到 ${mainComments.length} 个主评论组`);

      // 筛选包含关键词的评论组
      const keywordGroups = mainComments.filter(group =>
        this.groupHasKeywords(group, keywords)
      );

      console.log(`找到 ${keywordGroups.length} 个包含关键词的评论组`);

      // 按时间排序并限制数量
      keywordGroups.sort((a, b) => b.create_time - a.create_time);

      // 返回扁平化的评论列表，保持组结构
      const result_comments = [];
      for (let i = 0; i < Math.min(keywordGroups.length, limit); i++) {
        const group = keywordGroups[i];
        
        // 添加主评论
        result_comments.push({
          comment_id: group.comment_id,
          user_id: group.user_id,
          nickname: group.nickname,
          content: group.content,
          create_time: group.create_time,
          create_date: group.create_date,
          like_count: group.like_count,
          sub_comment_count: group.sub_comment_count,
          parent_comment_id: '0',
          is_reply: false,
          video_id: avNumber
        });

        // 添加所有回复
        if (group.replies && group.replies.length > 0) {
          group.replies.forEach(reply => {
            result_comments.push({
              comment_id: reply.comment_id,
              user_id: reply.user_id,
              nickname: reply.nickname,
              content: reply.content,
              create_time: reply.create_time,
              create_date: reply.create_date,
              like_count: reply.like_count,
              parent_comment_id: reply.parent_comment_id,
              is_reply: true,
              video_id: avNumber
            });
          });
        }
      }

      return result_comments;
    } catch (error) {
      console.error('优化关键词搜索失败:', error);
      // 如果优化搜索失败，回退到原始搜索
      return this.fallbackToOriginalSearch(avNumber, keywords, limit);
    }
  }

  // 获取最新评论（优化版）
  async getLatestCommentsOptimized(avNumber, limit = 3) {
    try {
      console.log(`使用优化搜索获取最新评论: 视频${avNumber}`);

      const result = await this.client.get({
        index: this.videoGroupIndex,
        id: avNumber
      });

      if (!result.body.found) {
        console.log(`视频 ${avNumber} 的分组数据未找到`);
        return [];
      }

      const videoData = result.body._source;
      const mainComments = videoData.main_comments || [];

      // 获取所有评论（主评论+回复）并按时间排序
      const allComments = [];
      
      mainComments.forEach(group => {
        // 添加主评论
        allComments.push({
          comment_id: group.comment_id,
          user_id: group.user_id,
          nickname: group.nickname,
          content: group.content,
          create_time: group.create_time,
          create_date: group.create_date,
          like_count: group.like_count,
          sub_comment_count: group.sub_comment_count,
          parent_comment_id: '0',
          is_reply: false,
          video_id: avNumber
        });

        // 添加回复
        if (group.replies && group.replies.length > 0) {
          group.replies.forEach(reply => {
            allComments.push({
              comment_id: reply.comment_id,
              user_id: reply.user_id,
              nickname: reply.nickname,
              content: reply.content,
              create_time: reply.create_time,
              create_date: reply.create_date,
              like_count: reply.like_count,
              parent_comment_id: reply.parent_comment_id,
              is_reply: true,
              video_id: avNumber
            });
          });
        }
      });

      // 按时间倒序排序，返回最新的评论
      allComments.sort((a, b) => b.create_time - a.create_time);
      
      return allComments.slice(0, limit);
    } catch (error) {
      console.error('优化获取最新评论失败:', error);
      return [];
    }
  }

  // 检查评论组是否包含关键词
  groupHasKeywords(group, keywords) {
    // 检查主评论是否包含关键词
    if (this.contentHasKeywords(group.content, keywords)) {
      return true;
    }

    // 检查任何回复是否包含关键词
    if (group.replies && group.replies.length > 0) {
      return group.replies.some(reply =>
        this.contentHasKeywords(reply.content, keywords)
      );
    }

    return false;
  }

  // 检查内容是否包含关键词
  contentHasKeywords(content, keywords) {
    if (!content || !keywords || keywords.length === 0) return false;

    const lowerContent = content.toLowerCase();
    return keywords.some(keyword =>
      lowerContent.includes(keyword.toLowerCase())
    );
  }

  // 回退到原始搜索方法
  async fallbackToOriginalSearch(avNumber, keywords, limit) {
    console.log('回退到原始搜索方法');
    
    try {
      // 这里可以调用原始的SearchService方法
      const SearchService = require('./SearchService');
      const originalService = new SearchService();
      return await originalService.getKeywordComments(avNumber, keywords, limit);
    } catch (error) {
      console.error('原始搜索也失败了:', error);
      return [];
    }
  }

  // 获取视频统计信息
  async getVideoStats(avNumber) {
    try {
      const result = await this.client.get({
        index: this.videoGroupIndex,
        id: avNumber
      });

      if (!result.body.found) {
        return null;
      }

      const videoData = result.body._source;
      const mainComments = videoData.main_comments || [];
      
      let totalReplies = 0;
      mainComments.forEach(group => {
        if (group.replies) {
          totalReplies += group.replies.length;
        }
      });

      return {
        video_id: videoData.video_id,
        total_comment_groups: mainComments.length,
        total_comments: videoData.comment_count,
        total_main_comments: mainComments.length,
        total_replies: totalReplies,
        indexed_at: videoData.indexed_at
      };
    } catch (error) {
      console.error('获取视频统计信息失败:', error);
      return null;
    }
  }

  // 检查分组索引是否存在
  async checkOptimizedIndexExists() {
    try {
      const exists = await this.client.indices.exists({
        index: this.videoGroupIndex
      });
      return exists.body;
    } catch (error) {
      console.error('检查分组索引失败:', error);
      return false;
    }
  }

  // 获取完整视频信息（包括元数据）
  async getVideoInfo(avNumber) {
    try {
      console.log(`开始获取视频信息: ${avNumber}`);

      // 检查MySQL连接
      if (!this.mysqlClient) {
        console.error('MySQL客户端未初始化');
        return null;
      }

      // 从MySQL获取视频基本信息
      const videoQuery = `
        SELECT
          video_id,
          title,
          \`desc\` as description,
          create_time,
          liked_count,
          video_play_count,
          video_danmaku,
          video_comment,
          video_url,
          video_cover_url,
          user_id,
          nickname,
          avatar
        FROM bilibili_video
        WHERE video_id = ?
      `;

      console.log(`执行MySQL查询: ${avNumber}`);
      const [videoRows] = await this.mysqlClient.execute(videoQuery, [avNumber]);
      console.log(`查询结果数量: ${videoRows.length}`);

      if (videoRows.length === 0) {
        console.log(`未找到视频: ${avNumber}`);
        return null;
      }

      const videoInfo = videoRows[0];
      console.log(`找到视频信息: ${videoInfo.title}`);

      // 获取评论统计信息
      const stats = await this.getVideoStats(avNumber);

      const result = {
        video_id: videoInfo.video_id,
        title: videoInfo.title,
        description: videoInfo.description,
        create_time: videoInfo.create_time,
        liked_count: videoInfo.liked_count,
        video_play_count: videoInfo.video_play_count,
        video_danmaku: videoInfo.video_danmaku,
        video_comment: videoInfo.video_comment,
        video_url: videoInfo.video_url,
        video_cover_url: videoInfo.video_cover_url,
        uploader: {
          user_id: videoInfo.user_id,
          nickname: videoInfo.nickname,
          avatar: videoInfo.avatar
        },
        comment_stats: stats || {
          total_comment_groups: 0,
          total_comments: 0,
          total_main_comments: 0,
          total_replies: 0
        }
      };

      console.log(`返回视频信息成功`);
      return result;
    } catch (error) {
      console.error("获取视频信息失败:", error);
      return null;
    }
  }
}

module.exports = OptimizedSearchService;
