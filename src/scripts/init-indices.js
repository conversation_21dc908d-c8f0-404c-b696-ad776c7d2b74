#!/usr/bin/env node

const { testElasticsearchConnection } = require('../config/database');
const ElasticsearchIndexService = require('../services/ElasticsearchIndexService');

async function main() {
  console.log('🔧 开始初始化 Elasticsearch 索引...');
  
  try {
    // 测试 Elasticsearch 连接
    console.log('🔍 测试 Elasticsearch 连接...');
    const elasticsearchConnected = await testElasticsearchConnection();
    
    if (!elasticsearchConnected) {
      throw new Error('Elasticsearch 连接失败');
    }
    
    console.log('✅ Elasticsearch 连接测试通过');
    
    // 初始化索引
    const indexService = new ElasticsearchIndexService();
    
    // 检查是否需要重建索引
    const args = process.argv.slice(2);
    if (args.includes('--rebuild')) {
      console.log('🔄 重建索引模式');
      await indexService.rebuildAllIndices();
    } else {
      await indexService.initializeAllIndices();
    }
    
    // 获取索引统计
    const stats = await indexService.getIndexStats();
    console.log(`📊 当前统计: 视频 ${stats.videos} 条, 评论 ${stats.comments} 条`);
    
    console.log('🎉 索引初始化完成!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ 索引初始化失败:', error);
    process.exit(1);
  }
}

// 运行主函数
main();
