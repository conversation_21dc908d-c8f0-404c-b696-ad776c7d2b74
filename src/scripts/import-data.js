#!/usr/bin/env node

const { testMysqlConnection, testElasticsearchConnection } = require('../config/database');
const ElasticsearchIndexService = require('../services/ElasticsearchIndexService');
const DataImportService = require('../services/DataImportService');

async function main() {
  console.log('🚀 开始数据导入脚本...');
  
  try {
    // 测试数据库连接
    console.log('🔍 测试数据库连接...');
    const mysqlConnected = await testMysqlConnection();
    const elasticsearchConnected = await testElasticsearchConnection();
    
    if (!mysqlConnected) {
      throw new Error('MySQL 连接失败');
    }
    
    if (!elasticsearchConnected) {
      throw new Error('Elasticsearch 连接失败');
    }
    
    console.log('✅ 数据库连接测试通过');
    
    // 初始化索引
    const indexService = new ElasticsearchIndexService();
    
    // 检查是否需要重建索引
    const args = process.argv.slice(2);
    if (args.includes('--rebuild')) {
      console.log('🔄 重建索引模式');
      await indexService.rebuildAllIndices();
    } else {
      await indexService.initializeAllIndices();
    }
    
    // 获取当前索引统计
    const beforeStats = await indexService.getIndexStats();
    console.log(`📊 导入前统计: 视频 ${beforeStats.videos} 条, 评论 ${beforeStats.comments} 条`);
    
    // 开始数据导入
    const importService = new DataImportService();
    const result = await importService.importAllData();
    
    // 获取导入后统计
    const afterStats = await indexService.getIndexStats();
    console.log(`📊 导入后统计: 视频 ${afterStats.videos} 条, 评论 ${afterStats.comments} 条`);
    
    console.log('🎉 数据导入脚本执行完成!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ 数据导入脚本执行失败:', error);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 运行主函数
main();
