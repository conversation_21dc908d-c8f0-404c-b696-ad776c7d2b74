#!/usr/bin/env node

const { testMysqlConnection, testElasticsearchConnection } = require('../config/database');
const DataImportService = require('../services/DataImportService');

async function main() {
  console.log('🧪 开始测试数据导入...');
  
  try {
    // 测试数据库连接
    console.log('🔍 测试数据库连接...');
    const mysqlConnected = await testMysqlConnection();
    const elasticsearchConnected = await testElasticsearchConnection();
    
    if (!mysqlConnected) {
      throw new Error('MySQL 连接失败');
    }
    
    if (!elasticsearchConnected) {
      throw new Error('Elasticsearch 连接失败');
    }
    
    console.log('✅ 数据库连接测试通过');
    
    // 测试小批量数据导入
    const importService = new DataImportService();
    
    console.log('📹 测试导入 5 条视频数据...');
    const videos = await importService.getVideosFromMySQL(5, 0);
    console.log(`获取到 ${videos.length} 条视频数据`);
    
    if (videos.length > 0) {
      const videoResult = await importService.importVideosToElasticsearch(videos);
      console.log(`视频导入结果: 成功 ${videoResult.success}, 失败 ${videoResult.failed}`);
    }
    
    console.log('💬 测试导入 10 条评论数据...');
    const comments = await importService.getCommentsFromMySQL(10, 0);
    console.log(`获取到 ${comments.length} 条评论数据`);
    
    if (comments.length > 0) {
      const commentResult = await importService.importCommentsToElasticsearch(comments);
      console.log(`评论导入结果: 成功 ${commentResult.success}, 失败 ${commentResult.failed}`);
    }
    
    console.log('🎉 测试导入完成!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ 测试导入失败:', error);
    console.error('错误详情:', error.stack);
    process.exit(1);
  }
}

// 运行主函数
main();
