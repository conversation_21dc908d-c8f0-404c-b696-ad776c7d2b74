const { getCurrentBeijingTimeString, timestampToBeijingTimeString } = require('../utils/timeUtils');

class Video {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.nickname = data.nickname;
    this.avatar = data.avatar;
    this.add_ts = data.add_ts;
    this.last_modify_ts = data.last_modify_ts;
    this.video_id = data.video_id;
    this.video_type = data.video_type;
    this.title = data.title;
    this.desc = data.desc;
    this.create_time = data.create_time;
    this.liked_count = data.liked_count;
    this.video_play_count = data.video_play_count;
    this.video_danmaku = data.video_danmaku;
    this.video_comment = data.video_comment;
    this.video_url = data.video_url;
    this.video_cover_url = data.video_cover_url;
    this.source_keyword = data.source_keyword;
  }

  // 转换为 Elasticsearch 文档格式
  toElasticsearchDoc() {
    return {
      id: this.id,
      user_id: this.user_id,
      nickname: this.nickname,
      avatar: this.avatar,
      add_ts: this.add_ts,
      last_modify_ts: this.last_modify_ts,
      video_id: this.video_id,
      av_number: this.extractAvNumber(),
      video_type: this.video_type,
      title: this.title,
      desc: this.desc,
      create_time: this.create_time,
      create_date: this.formatCreateDate(),
      liked_count: parseInt(this.liked_count) || 0,
      video_play_count: parseInt(this.video_play_count) || 0,
      video_danmaku: parseInt(this.video_danmaku) || 0,
      video_comment: parseInt(this.video_comment) || 0,
      video_url: this.video_url,
      video_cover_url: this.video_cover_url,
      source_keyword: this.source_keyword,
      indexed_at: getCurrentBeijingTimeString()
    };
  }

  // 从视频ID或URL中提取AV号
  extractAvNumber() {
    if (this.video_id) {
      return this.video_id.toString();
    }
    
    if (this.video_url && this.video_url.includes('/av')) {
      const match = this.video_url.match(/\/av(\d+)/);
      return match ? match[1] : null;
    }
    
    return null;
  }

  // 格式化创建日期
  formatCreateDate() {
    if (this.create_time) {
      const timestamp = parseInt(this.create_time);
      if (!isNaN(timestamp)) {
        return timestampToBeijingTimeString(timestamp);
      }
    }
    return null;
  }

  // 验证数据完整性
  isValid() {
    return this.video_id && this.title;
  }
}

module.exports = Video;
