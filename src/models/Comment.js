const { getCurrentBeijingTimeString, timestampToBeijingTimeString } = require('../utils/timeUtils');

class Comment {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.nickname = data.nickname;
    this.avatar = data.avatar;
    this.add_ts = data.add_ts;
    this.last_modify_ts = data.last_modify_ts;
    this.comment_id = data.comment_id;
    this.video_id = data.video_id;
    this.content = data.content;
    this.create_time = data.create_time;
    this.sub_comment_count = data.sub_comment_count;
    this.parent_comment_id = data.parent_comment_id;
    this.like_count = data.like_count;
  }

  // 转换为 Elasticsearch 文档格式
  toElasticsearchDoc() {
    return {
      id: this.id,
      user_id: this.user_id,
      nickname: this.nickname,
      avatar: this.avatar,
      add_ts: this.add_ts,
      last_modify_ts: this.last_modify_ts,
      comment_id: this.comment_id,
      video_id: this.video_id,
      av_number: this.extractAvNumber(),
      content: this.content,
      content_length: this.content ? this.content.length : 0,
      create_time: this.create_time,
      create_date: this.formatCreateDate(),
      sub_comment_count: parseInt(this.sub_comment_count) || 0,
      parent_comment_id: this.parent_comment_id,
      like_count: parseInt(this.like_count) || 0,
      is_reply: this.parent_comment_id && this.parent_comment_id !== '0',
      // keywords 不再自动提取，由API调用时指定
      indexed_at: getCurrentBeijingTimeString()
    };
  }

  // 从视频ID中提取AV号
  extractAvNumber() {
    if (this.video_id) {
      return this.video_id.toString();
    }
    return null;
  }

  // 格式化创建日期
  formatCreateDate() {
    if (this.create_time) {
      const timestamp = parseInt(this.create_time);
      if (!isNaN(timestamp)) {
        return timestampToBeijingTimeString(timestamp);
      }
    }
    return null;
  }

  // 检查是否包含指定关键词
  hasKeywords(keywords = []) {
    if (!keywords || keywords.length === 0) return false;
    if (!this.content) return false;

    const content = this.content.toLowerCase();
    return keywords.some(keyword =>
      content.includes(keyword.toLowerCase())
    );
  }

  // 验证数据完整性
  isValid() {
    return this.comment_id && this.video_id && this.content;
  }
}

module.exports = Comment;
