const mysql = require('mysql2/promise');
const { Client } = require('@elastic/elasticsearch');
require('dotenv').config();

// MySQL 连接配置
const mysqlConfig = {
  host: process.env.MYSQL_HOST,
  port: process.env.MYSQL_PORT,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Elasticsearch 连接配置
const elasticsearchConfig = {
  node: process.env.ELASTICSEARCH_URL,
  requestTimeout: 60000,
  pingTimeout: 3000,
  sniffOnStart: false
};

// 创建 MySQL 连接池
const mysqlPool = mysql.createPool(mysqlConfig);

// 创建 Elasticsearch 客户端
const elasticsearchClient = new Client(elasticsearchConfig);

// 测试 MySQL 连接
async function testMysqlConnection() {
  try {
    const connection = await mysqlPool.getConnection();
    console.log('MySQL 连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('MySQL 连接失败:', error.message);
    return false;
  }
}

// 测试 Elasticsearch 连接
async function testElasticsearchConnection() {
  try {
    const health = await elasticsearchClient.cluster.health();
    console.log('Elasticsearch 连接成功:', health.cluster_name);
    return true;
  } catch (error) {
    console.error('Elasticsearch 连接失败:', error.message);
    return false;
  }
}

module.exports = {
  mysqlPool,
  elasticsearchClient,
  testMysqlConnection,
  testElasticsearchConnection
};
