require('dotenv').config();

const config = {
  // 应用配置
  app: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  // Elasticsearch 配置
  elasticsearch: {
    url: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
    indices: {
      video: process.env.ELASTICSEARCH_INDEX_VIDEO || 'bilibili_videos',
      comment: process.env.ELASTICSEARCH_INDEX_COMMENT || 'bilibili_comments'
    }
  },

  // MySQL 配置
  mysql: {
    host: process.env.MYSQL_HOST,
    port: process.env.MYSQL_PORT,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE
  },

  // API 配置
  api: {
    rateLimit: {
      windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW_MS) || 60000, // 1分钟
      maxRequests: parseInt(process.env.API_RATE_LIMIT_MAX_REQUESTS) || 10000 // 无限制模式：10000次/分钟
    },
    pagination: {
      defaultLimit: 20,
      maxLimit: 1000 // 提高分页限制到1000
    }
  }
};

module.exports = config;
