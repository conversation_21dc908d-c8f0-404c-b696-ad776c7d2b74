const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const config = require('./config/app');
const { testMysqlConnection, testElasticsearchConnection } = require('./config/database');

const app = express();

// 中间件配置
app.use(helmet()); // 安全头
app.use(compression()); // 压缩响应
app.use(cors()); // 跨域支持
app.use(express.json({ limit: '10mb' })); // JSON 解析
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // URL 编码解析

// 设置字符编码处理
app.use((req, res, next) => {
  // 确保正确处理中文字符
  res.charset = 'utf-8';
  next();
});

// 速率限制已禁用 - 无限制模式
// const limiter = rateLimit({
//   windowMs: config.api.rateLimit.windowMs,
//   max: config.api.rateLimit.maxRequests,
//   message: {
//     error: 'Too many requests from this IP, please try again later.'
//   }
// });
// app.use('/api/', limiter);

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    const mysqlStatus = await testMysqlConnection();
    const elasticsearchStatus = await testElasticsearchConnection();
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        mysql: mysqlStatus ? 'connected' : 'disconnected',
        elasticsearch: elasticsearchStatus ? 'connected' : 'disconnected'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

// API 路由
app.use('/api/videos', require('./routes/videos'));
app.use('/api/search', require('./routes/search'));
app.use('/api/data', require('./routes/data'));

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('Error:', error);
  
  res.status(error.status || 500).json({
    error: config.app.env === 'development' ? error.message : 'Internal server error',
    ...(config.app.env === 'development' && { stack: error.stack })
  });
});

// 启动服务器
const PORT = config.app.port;
app.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${config.app.env}`);
  
  // 测试数据库连接
  await testMysqlConnection();
  await testElasticsearchConnection();
});

module.exports = app;
