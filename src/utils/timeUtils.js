/**
 * 时间工具类 - 处理时区转换
 */

/**
 * 将UTC时间转换为东八区时间字符串
 * @param {Date|number} input - Date对象或时间戳
 * @returns {string} 东八区时间字符串，格式：YYYY-MM-DDTHH:mm:ss.sss+08:00
 */
function toBeijingTimeString(input) {
  let date;
  
  if (input instanceof Date) {
    date = input;
  } else if (typeof input === 'number') {
    // 如果是秒级时间戳，转换为毫秒
    const timestamp = input < 10000000000 ? input * 1000 : input;
    date = new Date(timestamp);
  } else {
    date = new Date();
  }
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return null;
  }
  
  // 转换为东八区时间（UTC+8）
  const beijingTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
  
  // 格式化为ISO字符串，但替换Z为+08:00
  const isoString = beijingTime.toISOString();
  return isoString.replace('Z', '+08:00');
}

/**
 * 获取当前东八区时间字符串
 * @returns {string} 当前东八区时间字符串
 */
function getCurrentBeijingTimeString() {
  return toBeijingTimeString(new Date());
}

/**
 * 将时间戳转换为东八区时间字符串
 * @param {number} timestamp - 时间戳（秒或毫秒）
 * @returns {string|null} 东八区时间字符串或null（如果时间戳无效）
 */
function timestampToBeijingTimeString(timestamp) {
  if (!timestamp || isNaN(timestamp)) {
    return null;
  }
  
  return toBeijingTimeString(timestamp);
}

/**
 * 格式化显示时间（用于日志等）
 * @param {Date|number} input - Date对象或时间戳
 * @returns {string} 格式化的时间字符串，如：2025-07-07 19:30:45
 */
function formatDisplayTime(input) {
  const beijingTimeString = toBeijingTimeString(input);
  if (!beijingTimeString) {
    return 'Invalid Date';
  }
  
  // 转换为显示格式：YYYY-MM-DD HH:mm:ss
  return beijingTimeString.replace('T', ' ').replace(/\.\d{3}\+08:00$/, '');
}

module.exports = {
  toBeijingTimeString,
  getCurrentBeijingTimeString,
  timestampToBeijingTimeString,
  formatDisplayTime
};
