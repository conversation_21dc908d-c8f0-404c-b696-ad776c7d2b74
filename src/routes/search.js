const express = require('express');
const searchController = require('../controllers/searchController');
const router = express.Router();

// 全文搜索 - GET方法（保留兼容性）
router.get('/', searchController.search.bind(searchController));

// 全文搜索 - POST方法（推荐，支持中文）
router.post('/', searchController.searchPost.bind(searchController));

// 搜索建议
router.get('/suggest', searchController.suggest.bind(searchController));

// 热门搜索
router.get('/trending', searchController.trending.bind(searchController));

// 关键词统计
router.get('/keywords', searchController.keywordStats.bind(searchController));

module.exports = router;
