const express = require('express');
const dataController = require('../controllers/dataController');
const router = express.Router();

// 获取数据库连接状态
router.get('/status', dataController.getConnectionStatus.bind(dataController));

// 获取数据统计信息
router.get('/stats', dataController.getDataStats.bind(dataController));

// 清空Elasticsearch索引数据
router.post('/clear', dataController.clearElasticsearchData.bind(dataController));

// 导入最新数据（增量导入）
router.post('/import', dataController.importLatestData.bind(dataController));

// 清空并重新导入数据（全量刷新）
router.post('/refresh', dataController.refreshAllData.bind(dataController));

module.exports = router;
