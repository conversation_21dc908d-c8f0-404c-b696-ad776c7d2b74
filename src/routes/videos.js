const express = require('express');
const videoController = require('../controllers/videoController');
const router = express.Router();

// 搜索视频 - GET方法（保留兼容性）
router.get('/', videoController.searchVideos.bind(videoController));

// 搜索视频 - POST方法（推荐，支持中文）
router.post('/search', videoController.searchVideosPost.bind(videoController));

// 根据AV号获取视频和评论信息 - GET方法（保留兼容性）
router.get('/:av', videoController.getVideoByAv.bind(videoController));

// 根据AV号获取视频和评论信息 - POST方法（推荐，支持中文关键词）
router.post('/:av/keywords', videoController.getVideoByAvWithKeywords.bind(videoController));

// 获取视频的评论列表
router.get('/:av/comments', videoController.getVideoComments.bind(videoController));

// 获取视频统计信息
router.get('/:av/stats', videoController.getVideoStats.bind(videoController));

module.exports = router;
