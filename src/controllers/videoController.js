const SearchService = require('../services/SearchService');
const OptimizedSearchService = require('../services/OptimizedSearchService');

class VideoController {
  constructor() {
    this.searchService = new SearchService();
    this.optimizedSearchService = new OptimizedSearchService();
  }

  // 根据AV号获取视频和评论信息
  async getVideoByAv(req, res) {
    try {
      const { av } = req.params;
      const { keywords } = req.query; // 从查询参数获取关键词

      if (!av) {
        return res.status(400).json({
          success: false,
          message: 'AV号不能为空'
        });
      }

      // 处理关键词参数
      let keywordArray = [];
      if (keywords) {
        if (Array.isArray(keywords)) {
          keywordArray = keywords;
        } else if (typeof keywords === 'string') {
          // 支持逗号分隔的关键词字符串
          // 确保正确处理中文字符
          keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
        }
      }

      // 调试日志：显示处理后的关键词（生产环境中可以移除）
      if (keywordArray.length > 0) {
        console.log('处理的关键词:', keywordArray);
      }

      const result = await this.searchService.getVideoWithComments(av, keywordArray);
      
      if (!result) {
        return res.status(404).json({
          success: false,
          message: `未找到AV号为 ${av} 的视频`
        });
      }

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('获取视频信息失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 获取视频的评论列表
  async getVideoComments(req, res) {
    try {
      const { av } = req.params;
      const {
        page = 1,
        limit = 20,
        sortBy = 'create_time',
        sortOrder = 'desc',
        hasKeywords = false
      } = req.query;

      if (!av) {
        return res.status(400).json({
          success: false,
          message: 'AV号不能为空'
        });
      }

      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      const result = await this.searchService.searchCommentsByAv(av, {
        limit: parseInt(limit),
        offset,
        sortBy,
        sortOrder,
        hasKeywords: hasKeywords === 'true'
      });

      res.json({
        success: true,
        data: {
          total: result.total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(result.total / parseInt(limit)),
          comments: result.comments
        }
      });

    } catch (error) {
      console.error('获取评论列表失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 搜索视频
  async searchVideos(req, res) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20
      } = req.query;

      if (!query) {
        return res.status(400).json({
          success: false,
          message: '搜索关键词不能为空'
        });
      }

      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      const result = await this.searchService.fullTextSearch(query, {
        limit: parseInt(limit),
        offset,
        type: 'video'
      });

      res.json({
        success: true,
        data: {
          total: result.videos?.total || 0,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil((result.videos?.total || 0) / parseInt(limit)),
          videos: result.videos?.items || []
        }
      });

    } catch (error) {
      console.error('搜索视频失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 搜索视频 - POST方法（推荐，支持中文）
  async searchVideosPost(req, res) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20
      } = req.body;

      if (!query) {
        return res.status(400).json({
          success: false,
          message: '搜索关键词不能为空'
        });
      }

      console.log('POST搜索视频关键词:', query);

      const offset = (parseInt(page) - 1) * parseInt(limit);

      const result = await this.searchService.fullTextSearch(query, {
        limit: parseInt(limit),
        offset,
        type: 'video'
      });

      res.json({
        success: true,
        data: {
          total: result.videos?.total || 0,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil((result.videos?.total || 0) / parseInt(limit)),
          videos: result.videos?.items || []
        }
      });

    } catch (error) {
      console.error('POST搜索视频失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 获取视频统计信息
  async getVideoStats(req, res) {
    try {
      const { av } = req.params;
      
      if (!av) {
        return res.status(400).json({
          success: false,
          message: 'AV号不能为空'
        });
      }

      // 获取视频基本信息
      const videos = await this.searchService.searchVideoByAv(av);
      if (videos.length === 0) {
        return res.status(404).json({
          success: false,
          message: `未找到AV号为 ${av} 的视频`
        });
      }

      const video = videos[0];

      // 获取评论统计
      const commentStats = await this.searchService.searchCommentsByAv(av, { limit: 1 });
      const keywordComments = await this.searchService.getKeywordComments(av, 1);
      const duplicateRate = await this.searchService.calculateCommentDuplicateRate(av);

      res.json({
        success: true,
        data: {
          video: {
            av_number: video.av_number,
            title: video.title,
            video_play_count: video.video_play_count,
            liked_count: video.liked_count,
            video_comment: video.video_comment,
            create_date: video.create_date
          },
          comments: {
            total: commentStats.total,
            hasKeywords: keywordComments.length > 0,
            duplicateRate: parseFloat(duplicateRate)
          }
        }
      });

    } catch (error) {
      console.error('获取视频统计失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
  // POST方法处理中文关键词（避免URL编码问题）
  async getVideoByAvWithKeywords(req, res) {
    try {
      const { av } = req.params;
      const { keywords } = req.body;

      if (!av) {
        return res.status(400).json({
          success: false,
          message: 'AV号不能为空'
        });
      }

      // 处理关键词参数
      let keywordArray = [];
      if (keywords) {
        if (Array.isArray(keywords)) {
          keywordArray = keywords;
        } else if (typeof keywords === 'string') {
          keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
        }
      }

      console.log('POST请求处理的关键词:', keywordArray);

      const result = await this.searchService.getVideoWithComments(av, keywordArray);

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('获取视频信息失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 优化搜索方法（使用分组索引）
  async getVideoByAvOptimized(req, res) {
    try {
      const { av } = req.params;
      const { keywords } = req.body;

      if (!av) {
        return res.status(400).json({
          success: false,
          message: 'AV号不能为空'
        });
      }

      // 处理关键词参数
      let keywordArray = [];
      if (keywords) {
        if (Array.isArray(keywords)) {
          keywordArray = keywords;
        } else if (typeof keywords === 'string') {
          keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
        }
      }

      console.log('优化搜索处理的关键词:', keywordArray);

      // 检查优化索引是否存在
      const optimizedIndexExists = await this.optimizedSearchService.checkOptimizedIndexExists();

      if (!optimizedIndexExists) {
        console.log('优化索引不存在，回退到原始搜索');
        const result = await this.searchService.getVideoWithComments(av, keywordArray);
        return res.json({
          success: true,
          data: result,
          searchMethod: 'fallback'
        });
      }

      // 使用优化搜索
      console.time(`优化搜索-${av}`);

      const [latestComments, keywordComments, videoStats] = await Promise.all([
        this.optimizedSearchService.getLatestCommentsOptimized(av, 3),
        keywordArray.length > 0 ?
          this.optimizedSearchService.searchKeywordCommentsOptimized(av, keywordArray, 10) :
          [],
        this.optimizedSearchService.getVideoStats(av)
      ]);

      console.timeEnd(`优化搜索-${av}`);

      const result = {
        video: {
          av_number: av,
          video_id: av
        },
        latestComments,
        keywordComments,
        totalComments: videoStats ? videoStats.total_comments : 0,
        stats: videoStats,
        searchKeywords: keywordArray
      };

      res.json({
        success: true,
        data: result,
        searchMethod: 'optimized'
      });

    } catch (error) {
      console.error('优化搜索失败:', error);

      // 如果优化搜索失败，回退到原始搜索
      try {
        console.log('回退到原始搜索方法');
        const result = await this.searchService.getVideoWithComments(req.params.av, req.body.keywords);
        res.json({
          success: true,
          data: result,
          searchMethod: 'fallback_after_error'
        });
      } catch (fallbackError) {
        console.error('回退搜索也失败:', fallbackError);
        res.status(500).json({
          success: false,
          message: '搜索服务暂时不可用',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
      }
    }
  }
}

module.exports = new VideoController();
