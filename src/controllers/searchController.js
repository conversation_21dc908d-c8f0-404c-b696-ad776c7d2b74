const SearchService = require('../services/SearchService');

class SearchController {
  constructor() {
    this.searchService = new SearchService();
  }

  // 全文搜索
  async search(req, res) {
    try {
      const {
        q: query,
        type = 'all',
        page = 1,
        limit = 20
      } = req.query;

      if (!query) {
        return res.status(400).json({
          success: false,
          message: '搜索关键词不能为空'
        });
      }

      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      const result = await this.searchService.fullTextSearch(query, {
        limit: parseInt(limit),
        offset,
        type
      });

      // 格式化响应数据
      const response = {
        success: true,
        data: {
          query,
          type,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      };

      if (result.videos) {
        response.data.videos = {
          total: result.videos.total,
          totalPages: Math.ceil(result.videos.total / parseInt(limit)),
          items: result.videos.items
        };
      }

      if (result.comments) {
        response.data.comments = {
          total: result.comments.total,
          totalPages: Math.ceil(result.comments.total / parseInt(limit)),
          items: result.comments.items
        };
      }

      res.json(response);

    } catch (error) {
      console.error('全文搜索失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 全文搜索 - POST方法（推荐，支持中文）
  async searchPost(req, res) {
    try {
      const {
        q: query,
        type = 'all',
        page = 1,
        limit = 20
      } = req.body;

      if (!query) {
        return res.status(400).json({
          success: false,
          message: '搜索关键词不能为空'
        });
      }

      console.log('POST全文搜索关键词:', query, '类型:', type);

      const offset = (parseInt(page) - 1) * parseInt(limit);

      const result = await this.searchService.fullTextSearch(query, {
        limit: parseInt(limit),
        offset,
        type
      });

      // 格式化响应数据
      const response = {
        success: true,
        data: {
          query,
          type,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      };

      if (result.videos) {
        response.data.videos = {
          total: result.videos.total,
          totalPages: Math.ceil(result.videos.total / parseInt(limit)),
          items: result.videos.items
        };
      }

      if (result.comments) {
        response.data.comments = {
          total: result.comments.total,
          totalPages: Math.ceil(result.comments.total / parseInt(limit)),
          items: result.comments.items
        };
      }

      res.json(response);

    } catch (error) {
      console.error('POST全文搜索失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 搜索建议/自动完成
  async suggest(req, res) {
    try {
      const { q: query } = req.query;

      if (!query || query.length < 2) {
        return res.json({
          success: true,
          data: {
            suggestions: []
          }
        });
      }

      // 简单的搜索建议实现
      const videoResult = await this.searchService.fullTextSearch(query, {
        limit: 5,
        type: 'video'
      });

      const suggestions = videoResult.videos?.items.map(video => ({
        type: 'video',
        title: video.title,
        av_number: video.av_number,
        score: video._score
      })) || [];

      res.json({
        success: true,
        data: {
          query,
          suggestions: suggestions.slice(0, 5)
        }
      });

    } catch (error) {
      console.error('搜索建议失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 热门搜索
  async trending(req, res) {
    try {
      // 简单实现：返回播放量最高的视频
      const { elasticsearchClient } = require('../config/database');
      const config = require('../config/app');

      const response = await elasticsearchClient.search({
        index: config.elasticsearch.indices.video,
        body: {
          query: {
            match_all: {}
          },
          sort: [
            { video_play_count: { order: 'desc' } }
          ],
          size: 10
        }
      });

      const trending = response.hits.hits.map(hit => ({
        av_number: hit._source.av_number,
        title: hit._source.title,
        video_play_count: hit._source.video_play_count,
        liked_count: hit._source.liked_count
      }));

      res.json({
        success: true,
        data: {
          trending
        }
      });

    } catch (error) {
      console.error('获取热门搜索失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 关键词统计
  async keywordStats(req, res) {
    try {
      const { elasticsearchClient } = require('../config/database');
      const config = require('../config/app');

      // 获取包含关键词的评论统计
      const response = await elasticsearchClient.search({
        index: config.elasticsearch.indices.comment,
        body: {
          query: {
            bool: {
              must: [
                { exists: { field: 'keywords' } }
              ],
              must_not: [
                { term: { 'keywords.keyword': '' } }
              ]
            }
          },
          aggs: {
            keyword_stats: {
              terms: {
                field: 'keywords.keyword',
                size: 20
              }
            }
          },
          size: 0
        }
      });

      const keywordStats = response.aggregations.keyword_stats.buckets.map(bucket => ({
        keyword: bucket.key,
        count: bucket.doc_count
      }));

      res.json({
        success: true,
        data: {
          total: response.hits.total.value,
          keywords: keywordStats
        }
      });

    } catch (error) {
      console.error('获取关键词统计失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = new SearchController();
