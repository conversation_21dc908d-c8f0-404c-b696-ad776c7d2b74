const DataImportService = require('../services/DataImportService');
const ElasticsearchIndexService = require('../services/ElasticsearchIndexService');
const { testMysqlConnection, testElasticsearchConnection } = require('../config/database');

class DataController {
  constructor() {
    this.importService = new DataImportService();
    this.indexService = new ElasticsearchIndexService();
  }

  // 获取数据库连接状态
  async getConnectionStatus(req, res) {
    try {
      const mysqlStatus = await testMysqlConnection();
      const elasticsearchStatus = await testElasticsearchConnection();
      
      res.json({
        success: true,
        data: {
          mysql: {
            connected: mysqlStatus,
            status: mysqlStatus ? 'connected' : 'disconnected'
          },
          elasticsearch: {
            connected: elasticsearchStatus,
            status: elasticsearchStatus ? 'connected' : 'disconnected'
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '获取连接状态失败',
        details: error.message
      });
    }
  }

  // 获取数据统计信息
  async getDataStats(req, res) {
    try {
      // MySQL数据统计
      const mysqlCounts = await this.importService.getDataCounts();
      
      // Elasticsearch索引统计
      const elasticsearchStats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        data: {
          mysql: {
            videos: mysqlCounts.videos,
            comments: mysqlCounts.comments,
            total: mysqlCounts.videos + mysqlCounts.comments
          },
          elasticsearch: {
            videos: elasticsearchStats.videos,
            comments: elasticsearchStats.comments,
            total: elasticsearchStats.videos + elasticsearchStats.comments
          },
          sync_status: {
            videos_synced: elasticsearchStats.videos === mysqlCounts.videos,
            comments_synced: elasticsearchStats.comments === mysqlCounts.comments,
            fully_synced: (elasticsearchStats.videos === mysqlCounts.videos) && 
                         (elasticsearchStats.comments === mysqlCounts.comments)
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '获取数据统计失败',
        details: error.message
      });
    }
  }

  // 清空Elasticsearch索引数据
  async clearElasticsearchData(req, res) {
    try {
      console.log('🗑️ 开始清空Elasticsearch数据...');
      
      // 重建索引（删除并重新创建）
      await this.indexService.rebuildAllIndices();
      
      // 获取清空后的统计
      const stats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        message: 'Elasticsearch数据已清空',
        data: {
          videos: stats.videos,
          comments: stats.comments,
          cleared_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('清空Elasticsearch数据失败:', error);
      res.status(500).json({
        success: false,
        error: '清空数据失败',
        details: error.message
      });
    }
  }

  // 导入最新数据（全量导入）
  async importLatestData(req, res) {
    try {
      console.log('🚀 开始导入最新数据...');
      
      // 检查数据库连接
      const mysqlConnected = await testMysqlConnection();
      const elasticsearchConnected = await testElasticsearchConnection();
      
      if (!mysqlConnected) {
        return res.status(500).json({
          success: false,
          error: 'MySQL连接失败'
        });
      }
      
      if (!elasticsearchConnected) {
        return res.status(500).json({
          success: false,
          error: 'Elasticsearch连接失败'
        });
      }

      // 获取导入前统计
      const beforeStats = await this.indexService.getIndexStats();
      
      // 执行数据导入
      const importResult = await this.importService.importAllData();
      
      // 获取导入后统计
      const afterStats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        message: '数据导入完成',
        data: {
          import_result: importResult,
          before_stats: beforeStats,
          after_stats: afterStats,
          imported_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('导入数据失败:', error);
      res.status(500).json({
        success: false,
        error: '导入数据失败',
        details: error.message
      });
    }
  }

  // 清空并重新导入数据
  async refreshAllData(req, res) {
    try {
      console.log('🔄 开始刷新所有数据...');
      
      // 检查数据库连接
      const mysqlConnected = await testMysqlConnection();
      const elasticsearchConnected = await testElasticsearchConnection();
      
      if (!mysqlConnected) {
        return res.status(500).json({
          success: false,
          error: 'MySQL连接失败'
        });
      }
      
      if (!elasticsearchConnected) {
        return res.status(500).json({
          success: false,
          error: 'Elasticsearch连接失败'
        });
      }

      // 1. 清空现有数据
      console.log('🗑️ 清空现有索引...');
      await this.indexService.rebuildAllIndices();
      
      // 2. 导入最新数据
      console.log('📥 导入最新数据...');
      const importResult = await this.importService.importAllData();
      
      // 3. 获取最终统计
      const finalStats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        message: '数据刷新完成',
        data: {
          import_result: importResult,
          final_stats: finalStats,
          refreshed_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('刷新数据失败:', error);
      res.status(500).json({
        success: false,
        error: '刷新数据失败',
        details: error.message
      });
    }
  }
}

module.exports = new DataController();
