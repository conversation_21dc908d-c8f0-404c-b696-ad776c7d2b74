const DataImportService = require('../services/DataImportService');
const ElasticsearchIndexService = require('../services/ElasticsearchIndexService');
const { testMysqlConnection, testElasticsearchConnection } = require('../config/database');

class DataController {
  constructor() {
    this.importService = new DataImportService();
    this.indexService = new ElasticsearchIndexService();
  }

  // 获取数据库连接状态
  async getConnectionStatus(req, res) {
    try {
      const mysqlStatus = await testMysqlConnection();
      const elasticsearchStatus = await testElasticsearchConnection();
      
      res.json({
        success: true,
        data: {
          mysql: {
            connected: mysqlStatus,
            status: mysqlStatus ? 'connected' : 'disconnected'
          },
          elasticsearch: {
            connected: elasticsearchStatus,
            status: elasticsearchStatus ? 'connected' : 'disconnected'
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '获取连接状态失败',
        details: error.message
      });
    }
  }

  // 获取数据统计信息
  async getDataStats(req, res) {
    try {
      // MySQL数据统计
      const mysqlCounts = await this.importService.getDataCounts();
      
      // Elasticsearch索引统计
      const elasticsearchStats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        data: {
          mysql: {
            videos: mysqlCounts.videos,
            comments: mysqlCounts.comments,
            total: mysqlCounts.videos + mysqlCounts.comments
          },
          elasticsearch: {
            videos: elasticsearchStats.videos,
            comments: elasticsearchStats.comments,
            total: elasticsearchStats.videos + elasticsearchStats.comments
          },
          sync_status: {
            videos_synced: elasticsearchStats.videos === mysqlCounts.videos,
            comments_synced: elasticsearchStats.comments === mysqlCounts.comments,
            fully_synced: (elasticsearchStats.videos === mysqlCounts.videos) && 
                         (elasticsearchStats.comments === mysqlCounts.comments)
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: '获取数据统计失败',
        details: error.message
      });
    }
  }

  // 清空Elasticsearch索引数据
  async clearElasticsearchData(req, res) {
    try {
      console.log('🗑️ 开始清空Elasticsearch数据...');
      
      // 重建索引（删除并重新创建）
      await this.indexService.rebuildAllIndices();

      // 清空优化索引
      console.log('🗑️ 清空优化索引...');
      const { elasticsearchClient } = require('../config/database');
      const config = require('../config/app');
      const videoGroupIndex = config.elasticsearch.indices.videoGroup || 'bilibili_video_groups';

      try {
        const indexExists = await elasticsearchClient.indices.exists({
          index: videoGroupIndex
        });

        if (indexExists.body) {
          await elasticsearchClient.indices.delete({
            index: videoGroupIndex
          });
          console.log(`✅ 优化索引 ${videoGroupIndex} 已删除`);
        }
      } catch (error) {
        console.log(`⚠️ 清空优化索引失败: ${error.message}`);
      }

      // 获取清空后的统计
      const stats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        message: 'Elasticsearch数据已清空',
        data: {
          videos: stats.videos,
          comments: stats.comments,
          cleared_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('清空Elasticsearch数据失败:', error);
      res.status(500).json({
        success: false,
        error: '清空数据失败',
        details: error.message
      });
    }
  }

  // 导入最新数据（全量导入）
  async importLatestData(req, res) {
    try {
      console.log('🚀 开始导入最新数据...');
      
      // 检查数据库连接
      const mysqlConnected = await testMysqlConnection();
      const elasticsearchConnected = await testElasticsearchConnection();
      
      if (!mysqlConnected) {
        return res.status(500).json({
          success: false,
          error: 'MySQL连接失败'
        });
      }
      
      if (!elasticsearchConnected) {
        return res.status(500).json({
          success: false,
          error: 'Elasticsearch连接失败'
        });
      }

      // 获取导入前统计
      const beforeStats = await this.indexService.getIndexStats();
      
      // 执行数据导入
      const importResult = await this.importService.importAllData();

      // 导入优化索引数据
      console.log('🚀 导入优化索引数据...');
      const OptimizedDataImportService = require('../services/OptimizedDataImportService');
      const optimizedImportService = new OptimizedDataImportService();
      const optimizedResult = await optimizedImportService.executeOptimizedImport();

      // 获取导入后统计
      const afterStats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        message: '数据导入完成',
        data: {
          import_result: importResult,
          optimized_import_result: optimizedResult,
          before_stats: beforeStats,
          after_stats: afterStats,
          imported_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('导入数据失败:', error);
      res.status(500).json({
        success: false,
        error: '导入数据失败',
        details: error.message
      });
    }
  }

  // 清空并重新导入数据
  async refreshAllData(req, res) {
    try {
      console.log('🔄 开始刷新所有数据...');
      
      // 检查数据库连接
      const mysqlConnected = await testMysqlConnection();
      const elasticsearchConnected = await testElasticsearchConnection();
      
      if (!mysqlConnected) {
        return res.status(500).json({
          success: false,
          error: 'MySQL连接失败'
        });
      }
      
      if (!elasticsearchConnected) {
        return res.status(500).json({
          success: false,
          error: 'Elasticsearch连接失败'
        });
      }

      // 1. 清空现有数据
      console.log('🗑️ 清空现有索引...');
      await this.indexService.rebuildAllIndices();

      // 清空优化索引
      console.log('🗑️ 清空优化索引...');
      const { elasticsearchClient } = require('../config/database');
      const config = require('../config/app');
      const videoGroupIndex = config.elasticsearch.indices.videoGroup || 'bilibili_video_groups';

      try {
        const indexExists = await elasticsearchClient.indices.exists({
          index: videoGroupIndex
        });

        if (indexExists.body) {
          await elasticsearchClient.indices.delete({
            index: videoGroupIndex
          });
          console.log(`✅ 优化索引 ${videoGroupIndex} 已删除`);
        }
      } catch (error) {
        console.log(`⚠️ 清空优化索引失败: ${error.message}`);
      }

      // 2. 导入最新数据
      console.log('📥 导入最新数据...');
      const importResult = await this.importService.importAllData();

      // 3. 导入优化索引数据
      console.log('🚀 导入优化索引数据...');
      const OptimizedDataImportService = require('../services/OptimizedDataImportService');
      const optimizedImportService = new OptimizedDataImportService();
      const optimizedResult = await optimizedImportService.executeOptimizedImport();

      // 4. 获取最终统计
      const finalStats = await this.indexService.getIndexStats();
      
      res.json({
        success: true,
        message: '数据刷新完成',
        data: {
          import_result: importResult,
          optimized_import_result: optimizedResult,
          final_stats: finalStats,
          refreshed_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('刷新数据失败:', error);
      res.status(500).json({
        success: false,
        error: '刷新数据失败',
        details: error.message
      });
    }
  }

  // 重建优化索引
  async rebuildOptimizedIndex(req, res) {
    try {
      console.log('🔧 开始重建优化索引...');

      // 检查数据库连接
      const mysqlConnected = await testMysqlConnection();
      const elasticsearchConnected = await testElasticsearchConnection();

      if (!mysqlConnected) {
        return res.status(500).json({
          success: false,
          error: 'MySQL连接失败'
        });
      }

      if (!elasticsearchConnected) {
        return res.status(500).json({
          success: false,
          error: 'Elasticsearch连接失败'
        });
      }

      // 重建优化索引
      const OptimizedDataImportService = require('../services/OptimizedDataImportService');
      const optimizedImportService = new OptimizedDataImportService();

      // 删除现有优化索引
      console.log('🗑️ 删除现有优化索引...');
      const { elasticsearchClient } = require('../config/database');
      const config = require('../config/app');
      const videoGroupIndex = config.elasticsearch.indices.videoGroup || 'bilibili_video_groups';

      try {
        const indexExists = await elasticsearchClient.indices.exists({
          index: videoGroupIndex
        });

        if (indexExists.body) {
          await elasticsearchClient.indices.delete({
            index: videoGroupIndex
          });
          console.log(`✅ 优化索引 ${videoGroupIndex} 已删除`);
        }
      } catch (error) {
        console.log(`⚠️ 删除优化索引失败: ${error.message}`);
      }

      // 重新创建优化索引
      console.log('🚀 重新创建优化索引...');
      const result = await optimizedImportService.executeOptimizedImport();

      res.json({
        success: true,
        message: '优化索引重建完成',
        data: {
          optimized_import_result: result,
          rebuilt_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('重建优化索引失败:', error);
      res.status(500).json({
        success: false,
        error: '重建优化索引失败',
        details: error.message
      });
    }
  }
}

module.exports = new DataController();
