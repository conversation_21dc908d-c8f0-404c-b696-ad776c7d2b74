dcoker elasticsearch
从数据库中导入数据到clasticsearch 
数据库 1.15.151.6 3307端口
mysql -u root -p123456 media_crawler -e 
表名
    'bilibili_video_comment' 

    'bilibili_video' as '
/Users/<USER>/Desktop/数据库el/数据库文件 参考该目录表的具体数据行格式

需要返回的数据
通过 av号确定需要获取的视频以及对应的评论
然后通过筛选 排序等来返回数据 提供一个API接口 通过get可以获取到指定数据 返回 带"record_id": "recuQdKB3t761X" 的数据
需要返回这些数据
[
  {
    "code": 0,
    "data": {
      "has_more": true,
      "items": [
        {
          "field_id": "fld7vjj1Ox",
          "field_name": "投放日期",
          "is_primary": true,
          "property": null,
          "type": 1,
          "ui_type": "Text"
        },
        {
          "field_id": "fld4Le0NCg",
          "field_name": "文章标题",
          "is_primary": false,
          "property": null,
          "type": 1,
          "ui_type": "Text"
        },

        {
          "field_id": "fld4OX5vD3",
          "field_name": "账号名称",
          "is_primary": false,
          "property": null,
          "type": 1,
          "ui_type": "Text"
        },
 
评论数量

  
        {
          "field_id": "fldesSGWPT",
          "field_name": "最新评论日期",
          "is_primary": false,
          "property": {
            "formatter": "0"
          },
          "type": 2,
          "ui_type": "Number"
        },
        {
          "field_id": "fldA6Ygs1a",
          "field_name": "最新评论内容单条（3条）",
          "is_primary": false,
          "property": null,
          "type": 1,
          "ui_type": "Text"
        },
        {
          "field_id": "fldI4gMcXV",
          "field_name": "含关键词评论（组）",
          "is_primary": false,
          "property": null,
          "type": 1,
          "ui_type": "Text"
        },
        {
          "field_id": "fldi2rnsCl",
          "field_name": "是否有渠道",
          "is_primary": false,
          "property": null,
          "type": 1,
          "ui_type": "Text"
        },
        {
          "field_id": "fldz8OO6Oz",
          "field_name": "评论区库重复率",
          "is_primary": false,
          "property": {
            "formatter": "0.0"
          },
          "type": 2,
          "ui_type": "Number"
        },

        {
          "field_id": "fldkv3mADq",
          "field_name": "视频投放人uid",
          "is_primary": false,
          "property": {
            "formatter": "0"
 
]