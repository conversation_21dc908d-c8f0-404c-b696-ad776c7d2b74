#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 打包Bilibili数据检索系统为离线Docker镜像 ===${NC}"
echo -e "${YELLOW}目标架构: x86_64 (amd64)${NC}"
echo -e "${YELLOW}当前系统: $(uname -m)${NC}"
echo

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

# 创建打包目录
PACKAGE_DIR="bilibili-system-offline"
IMAGES_DIR="$PACKAGE_DIR/docker-images"
SCRIPTS_DIR="$PACKAGE_DIR/scripts"

echo -e "${YELLOW}📁 创建打包目录...${NC}"
rm -rf "$PACKAGE_DIR"
mkdir -p "$IMAGES_DIR"
mkdir -p "$SCRIPTS_DIR"

# 定义需要的镜像列表
IMAGES=(
    "docker.elastic.co/elasticsearch/elasticsearch:8.11.0"
    "node:18-alpine"
)

echo -e "${YELLOW}🔄 拉取并保存Docker镜像 (x86_64架构)...${NC}"

# 拉取并保存每个镜像
for image in "${IMAGES[@]}"; do
    echo -e "${BLUE}处理镜像: $image${NC}"
    
    # 拉取指定架构的镜像
    echo -e "${YELLOW}  拉取镜像...${NC}"
    if docker pull --platform linux/amd64 "$image"; then
        echo -e "${GREEN}  ✅ 拉取成功${NC}"
    else
        echo -e "${RED}  ❌ 拉取失败${NC}"
        exit 1
    fi
    
    # 保存镜像为tar文件
    image_name=$(echo "$image" | sed 's/[\/:]/_/g')
    echo -e "${YELLOW}  保存镜像为 ${image_name}.tar...${NC}"
    if docker save "$image" -o "$IMAGES_DIR/${image_name}.tar"; then
        echo -e "${GREEN}  ✅ 保存成功${NC}"
    else
        echo -e "${RED}  ❌ 保存失败${NC}"
        exit 1
    fi
    echo
done

# 构建API镜像
echo -e "${YELLOW}🏗️  构建API镜像...${NC}"
if docker build --platform linux/amd64 -t bilibili-api:latest .; then
    echo -e "${GREEN}✅ API镜像构建成功${NC}"
else
    echo -e "${RED}❌ API镜像构建失败${NC}"
    exit 1
fi

# 保存API镜像
echo -e "${YELLOW}💾 保存API镜像...${NC}"
if docker save bilibili-api:latest -o "$IMAGES_DIR/bilibili-api_latest.tar"; then
    echo -e "${GREEN}✅ API镜像保存成功${NC}"
else
    echo -e "${RED}❌ API镜像保存失败${NC}"
    exit 1
fi

# 复制项目文件
echo -e "${YELLOW}📋 复制项目文件...${NC}"
cp -r src "$PACKAGE_DIR/"
cp package.json "$PACKAGE_DIR/"
cp package-lock.json "$PACKAGE_DIR/"
cp Dockerfile "$PACKAGE_DIR/"
cp docker-compose.yml "$PACKAGE_DIR/"
cp .env "$PACKAGE_DIR/"
cp API_DOCUMENTATION.md "$PACKAGE_DIR/"
cp POST_API_USAGE_GUIDE.md "$PACKAGE_DIR/"

# 创建离线部署脚本
echo -e "${YELLOW}📝 创建离线部署脚本...${NC}"

cat > "$SCRIPTS_DIR/离线部署.sh" << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Bilibili数据检索系统 - 离线部署 ===${NC}"
echo

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker运行正常${NC}"

# 进入项目目录
cd "$(dirname "$0")/.."

# 加载Docker镜像
echo -e "${YELLOW}📦 加载Docker镜像...${NC}"
for tar_file in docker-images/*.tar; do
    if [ -f "$tar_file" ]; then
        echo -e "${BLUE}加载: $(basename "$tar_file")${NC}"
        if docker load -i "$tar_file"; then
            echo -e "${GREEN}✅ 加载成功${NC}"
        else
            echo -e "${RED}❌ 加载失败${NC}"
            exit 1
        fi
    fi
done

echo

# 停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
docker-compose down -v

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 15

# 检查Elasticsearch
echo -e "${YELLOW}🔍 检查Elasticsearch...${NC}"
for i in {1..6}; do
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待Elasticsearch启动... ($i/6)${NC}"
        sleep 10
    fi
    
    if [ $i -eq 6 ]; then
        echo -e "${RED}❌ Elasticsearch启动失败${NC}"
        docker-compose logs elasticsearch
        exit 1
    fi
done

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务...${NC}"
for i in {1..3}; do
    if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待API服务启动... ($i/3)${NC}"
        sleep 5
    fi
    
    if [ $i -eq 3 ]; then
        echo -e "${RED}❌ API服务启动失败${NC}"
        docker-compose logs api
        exit 1
    fi
done

echo
echo -e "${GREEN}🎉 离线部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务地址：${NC}"
echo "• API服务: http://localhost:3000"
echo "• Elasticsearch: http://localhost:9200"
echo
echo -e "${BLUE}🔧 下一步操作：${NC}"
echo "• 导入数据: curl -X POST http://localhost:3000/api/data/refresh"
echo "• 查看状态: curl http://localhost:3000/api/data/status"
echo "• 查看日志: docker-compose logs -f"
EOF

# 创建数据导入脚本
cat > "$SCRIPTS_DIR/导入数据.sh" << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 导入Bilibili数据 ===${NC}"
echo

# 检查API服务
if ! curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
    echo -e "${RED}❌ API服务未运行，请先运行离线部署脚本${NC}"
    exit 1
fi

echo -e "${GREEN}✅ API服务运行正常${NC}"

# 导入数据
echo -e "${YELLOW}📥 开始导入数据...${NC}"
echo "这可能需要几分钟时间，请耐心等待..."

if curl -X POST http://localhost:3000/api/data/refresh; then
    echo
    echo -e "${GREEN}🎉 数据导入完成！${NC}"
    echo
    echo -e "${BLUE}📊 查看数据统计：${NC}"
    curl -s http://localhost:3000/api/data/stats | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/data/stats
else
    echo
    echo -e "${RED}❌ 数据导入失败${NC}"
    exit 1
fi
EOF

# 创建测试脚本
cat > "$SCRIPTS_DIR/测试系统.sh" << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 系统功能测试 ===${NC}"
echo

# 检查API服务
if ! curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
    echo -e "${RED}❌ API服务未运行${NC}"
    exit 1
fi

echo -e "${GREEN}✅ API服务运行正常${NC}"
echo

# 测试视频搜索
echo -e "${BLUE}🔍 测试视频搜索...${NC}"
curl -s -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "limit": 2}' | jq '.' 2>/dev/null || \
curl -s -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "limit": 2}'

echo
echo -e "${GREEN}🎉 测试完成！${NC}"
EOF

# 设置脚本执行权限
chmod +x "$SCRIPTS_DIR"/*.sh

# 创建README文件
cat > "$PACKAGE_DIR/README.md" << 'EOF'
# Bilibili数据检索系统 - 离线部署包

## 📦 包含内容

- **Docker镜像**: 预构建的Elasticsearch和API服务镜像
- **源代码**: 完整的Node.js API服务代码
- **配置文件**: Docker Compose和环境配置
- **部署脚本**: 一键离线部署脚本
- **文档**: API使用文档和说明

## 🚀 快速部署

### 1. 解压部署包
```bash
tar -xzf bilibili-system-offline.tar.gz
cd bilibili-system-offline
```

### 2. 运行离线部署
```bash
./scripts/离线部署.sh
```

### 3. 导入数据
```bash
./scripts/导入数据.sh
```

### 4. 测试系统
```bash
./scripts/测试系统.sh
```

## 📋 服务地址

- **API服务**: http://localhost:3000
- **Elasticsearch**: http://localhost:9200

## 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

## 📚 API文档

详细的API使用说明请查看：
- `API_DOCUMENTATION.md` - 完整API文档
- `POST_API_USAGE_GUIDE.md` - POST方法使用指南

## 🎯 系统特性

- ✅ 离线部署，无需外网连接
- ✅ x86_64架构兼容
- ✅ 包含45个视频和4105条评论数据
- ✅ 支持中文全文搜索
- ✅ RESTful API接口
- ✅ 评论关键词分析
- ✅ 重复率计算

## 💡 注意事项

1. 确保Docker已安装并运行
2. 确保端口3000和9200未被占用
3. 建议系统内存至少4GB
4. 首次启动需要等待Elasticsearch初始化
EOF

echo -e "${YELLOW}📦 创建压缩包...${NC}"
tar -czf bilibili-system-offline.tar.gz "$PACKAGE_DIR"

# 获取文件大小
PACKAGE_SIZE=$(du -h bilibili-system-offline.tar.gz | cut -f1)

echo
echo -e "${GREEN}🎉 打包完成！${NC}"
echo
echo -e "${BLUE}📦 离线部署包信息：${NC}"
echo "• 文件名: bilibili-system-offline.tar.gz"
echo "• 大小: $PACKAGE_SIZE"
echo "• 架构: x86_64 (amd64)"
echo
echo -e "${BLUE}📋 包含的Docker镜像：${NC}"
for image in "${IMAGES[@]}"; do
    echo "• $image"
done
echo "• bilibili-api:latest (自构建)"
echo
echo -e "${BLUE}🚀 部署说明：${NC}"
echo "1. 将 bilibili-system-offline.tar.gz 传输到目标服务器"
echo "2. 解压: tar -xzf bilibili-system-offline.tar.gz"
echo "3. 进入目录: cd bilibili-system-offline"
echo "4. 运行部署: ./scripts/离线部署.sh"
echo "5. 导入数据: ./scripts/导入数据.sh"
echo
echo -e "${YELLOW}💡 提示：目标服务器需要安装Docker和Docker Compose${NC}"
