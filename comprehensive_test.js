const http = require('http');

// 全面测试中文字符处理
const runComprehensiveTest = async () => {
  console.log('=== Bilibili API 中文字符处理全面测试 ===\n');
  
  const testCases = [
    {
      name: '单个中文关键词: 备考',
      keywords: '备考',
      expected: '> 0'
    },
    {
      name: '单个中文关键词: 考试',
      keywords: '考试',
      expected: '> 0'
    },
    {
      name: '单个中文关键词: 执业药师',
      keywords: '执业药师',
      expected: '> 0'
    },
    {
      name: '多个中文关键词: 备考,考试',
      keywords: '备考,考试',
      expected: '> 0'
    },
    {
      name: '多个中文关键词: 备考,考试,诺石',
      keywords: '备考,考试,诺石',
      expected: '> 0'
    },
    {
      name: '不存在的关键词: 不存在的词',
      keywords: '不存在的词',
      expected: '= 0'
    },
    {
      name: '无关键词参数',
      keywords: null,
      expected: '= 0'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`测试: ${testCase.name}`);
    
    let url = 'http://localhost:3000/api/videos/113599301292265';
    if (testCase.keywords) {
      url += `?keywords=${encodeURIComponent(testCase.keywords)}`;
    }
    
    try {
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.success) {
        const keywordCount = data.data.keywordComments.length;
        const duplicateRate = data.data.stats.duplicateRate;
        const totalComments = data.data.stats.totalComments;
        
        console.log(`  ✅ 成功`);
        console.log(`     关键词评论数量: ${keywordCount} (期望 ${testCase.expected})`);
        console.log(`     总评论数: ${totalComments}`);
        console.log(`     重复率: ${duplicateRate}%`);
        
        // 验证期望结果
        if (testCase.expected === '= 0' && keywordCount === 0) {
          console.log(`     ✅ 结果符合期望`);
        } else if (testCase.expected === '> 0' && keywordCount > 0) {
          console.log(`     ✅ 结果符合期望`);
        } else {
          console.log(`     ❌ 结果不符合期望`);
        }
      } else {
        console.log(`  ❌ API错误: ${data.message}`);
      }
    } catch (error) {
      console.log(`  ❌ 请求错误: ${error.message}`);
    }
    
    console.log('');
  }
  
  // 测试响应时间
  console.log('=== 性能测试 ===');
  const startTime = Date.now();
  
  try {
    const response = await fetch('http://localhost:3000/api/videos/113599301292265?keywords=备考,考试');
    const data = await response.json();
    const endTime = Date.now();
    
    if (data.success) {
      console.log(`响应时间: ${endTime - startTime}ms`);
      console.log(`关键词评论数量: ${data.data.keywordComments.length}`);
      console.log(`重复率: ${data.data.stats.duplicateRate}%`);
      console.log('✅ 性能测试通过');
    }
  } catch (error) {
    console.log(`❌ 性能测试失败: ${error.message}`);
  }
  
  console.log('\n=== 测试完成 ===');
};

// 检查fetch是否可用
if (typeof fetch === 'undefined') {
  console.log('需要Node.js 18+或安装node-fetch');
  process.exit(1);
}

runComprehensiveTest();
