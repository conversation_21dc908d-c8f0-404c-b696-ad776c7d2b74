# Docker环境专用配置
NODE_ENV=production
PORT=3000

# Elasticsearch 配置（Docker内部网络）
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_INDEX_VIDEO=bilibili_videos
ELASTICSEARCH_INDEX_COMMENT=bilibili_comments

# MySQL 配置（外部数据库）
MYSQL_HOST=**********
MYSQL_PORT=3307
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=media_crawler

# API 配置 - 无限制模式
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX_REQUESTS=10000
