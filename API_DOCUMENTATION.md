# Bilibili 视频评论检索系统 API 文档

## 系统架构

本系统基于 **Elasticsearch** 构建，提供高效的视频和评论检索服务。

### 数据结构
- **视频索引**: `bilibili_videos` - 存储45条视频数据
- **评论索引**: `bilibili_comments` - 存储4105条评论数据

## 评论分级结构

### 一级评论（主评论）
- `parent_comment_id` = "0" 或 null
- 代表对视频的直接评论

### 二级评论（回复）
- `parent_comment_id` = 主评论的 `comment_id`
- 代表对主评论的回复

## 核心API接口

### 1. 根据AV号获取完整视频信息

```
POST /api/videos/{av}/keywords
Content-Type: application/json

{
  "keywords": "执业药师"  // 或 ["执业药师", "备考", "考试"]
}
```

#### 参数说明：
- `keywords` (可选): 关键词参数，支持以下格式：
  - 字符串格式：`{"keywords": "执业药师"}`
  - 逗号分隔：`{"keywords": "备考,考试,诺石"}`
  - 数组格式：`{"keywords": ["执业药师", "备考", "考试"]}`
  - 如果不提供此参数，`keywordComments` 将返回空数组

#### 示例请求：

```bash
# 单个关键词
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'

# 多个关键词（逗号分隔）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "备考,考试,诺石"}'

# 多个关键词（数组格式）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师", "备考", "考试"]}'

# 不指定关键词（获取所有信息）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 返回数据结构：

##### 视频基本信息
```json
{
  "video": {
    "id": 331,
    "user_id": "691521358",
    "nickname": "至尊菠萝牛肉芝士汉堡",
    "av_number": "113599301292265",
    "title": "VLOG打卡|跟着诺石打卡执业药师第四天~",
    "video_play_count": 11035,
    "liked_count": 2,
    "video_comment": 83,
    "create_date": "2024-12-05T08:40:51.000Z"
  }
}
```

##### 统计信息
```json
{
  "stats": {
    "totalComments": 83,        // 总评论数
    "duplicateRate": 5,         // 重复率（百分比）
    "keywordCommentGroups": 5,  // 关键词评论组数
    "total": 83,               // 总数（同totalComments）
    "uniqueUsers": 0,          // 独特用户数
    "likeStats": {             // 点赞统计
      "avg": 0,                // 平均点赞数
      "max": 0,                // 最大点赞数
      "min": 0,                // 最小点赞数
      "sum": 0                 // 总点赞数
    },
    "contentLengthStats": {    // 评论长度统计
      "avg": 42,               // 平均长度
      "max": 167,              // 最大长度
      "min": 5                 // 最小长度
    },
    "replyStats": {            // 回复统计
      "replies": 0,            // 回复评论数
      "directComments": 0      // 直接评论数
    },
    "keywordStats": []         // 关键词统计（如果有关键词）
  }
}
```

> **优化说明**: 为了减少API响应大小和提高性能，已移除 `dailyComments` 每日评论分布数据。优化后响应大小从约18KB减少到约11KB。

**重复率计算说明**：
- 使用编辑距离算法（Levenshtein Distance）计算评论组之间的文本相似度
- 相似度阈值：70%（即编辑距离/最大长度 ≤ 0.3）
- 基于评论组进行比较，而非单个评论
- 性能优化：最多比较20个评论组

##### 最新评论（不区分一级二级）
```json
{
  "latestComments": [
    {
      "comment_id": "267494640560",
      "parent_comment_id": "267492731856",  // 非"0"表示这是回复
      "content": "有时候就是这么简单...",
      "create_date": "2025-07-03T10:15:15.000Z",
      "is_reply": true,
      "keywords": []
    }
  ]
}
```

##### 关键词评论组
**重要特性**：
- **参数化关键词**：通过 `keywords` 参数指定，不再自动检测
- 按评论组返回（主评论 + 其所有回复）
- 只要组内任一评论包含关键词，返回整组
- 限制返回5组，避免数据过多
- **中文支持**：直接支持中文关键词，无需特殊编码

```json
{
  "keywordComments": [
    // 第一组：主评论包含关键词
    {
      "comment_id": "265597961569",
      "parent_comment_id": "0",           // "0"表示主评论
      "content": "第一次备考，四科一起学还是分两年？",
      "keywords": ["备考"],
      "is_reply": false
    },
    // 该组的回复
    {
      "comment_id": "265598731777",
      "parent_comment_id": "265597961569", // 指向主评论ID
      "content": "时间紧就先攻药一、药二...",
      "keywords": [],
      "is_reply": true
    },
    // 第二组：主评论
    {
      "comment_id": "264334549569",
      "parent_comment_id": "0",
      "content": "听说今年考试改版了？",
      "keywords": ["考试"],
      "is_reply": false
    }
  ]
}
```

### 2. 获取评论列表（支持筛选）
```
GET /api/videos/{av}/comments?page=1&limit=20&hasKeywords=true&sortBy=create_time&sortOrder=desc
```

#### 参数说明：
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `hasKeywords`: 是否只返回包含关键词的评论（true/false）
- `sortBy`: 排序字段（create_time, like_count等）
- `sortOrder`: 排序方向（asc/desc）

### 3. 获取视频统计信息
```
GET /api/videos/{av}/stats
```

### 4. 搜索视频
```
POST /api/videos/search
Content-Type: application/json

{
  "q": "诺石",
  "page": 1,
  "limit": 20
}
```

### 5. 全文搜索
```
POST /api/search
Content-Type: application/json

{
  "q": "执业药师",
  "type": "all",
  "page": 1,
  "limit": 20
}
```

#### type参数：
- `all`: 搜索视频和评论
- `video`: 只搜索视频
- `comment`: 只搜索评论

### 6. 热门视频
```
GET /api/search/trending
```

### 7. 关键词统计
```
GET /api/search/keywords
```

## 统一使用POST方法的优势

### 🎯 为什么推荐POST方法？

1. **完美支持中文字符**
   - 无需URL编码，直接使用原始中文字符
   - 避免终端编码问题
   - 支持复杂的参数结构

2. **更好的参数传递**
   - JSON格式清晰易读
   - 支持嵌套对象和数组
   - 类型安全

3. **避免URL长度限制**
   - GET请求URL有长度限制
   - POST请求体可以传递更多数据

### 📋 完整API接口列表

| 接口 | 方法 | 用途 |
|------|------|------|
| `/api/videos/{av}/keywords` | POST | 获取视频信息和关键词评论 |
| `/api/videos/{av}/comments` | GET | 获取评论列表（支持筛选） |
| `/api/videos/{av}/stats` | GET | 获取视频统计信息 |
| `/api/videos/search` | POST | 搜索视频 |
| `/api/search` | POST | 全文搜索 |
| `/api/search/trending` | GET | 获取热门视频 |
| `/api/search/keywords` | GET | 获取关键词统计 |
| `/api/data/status` | GET | 检查数据库连接状态 |
| `/api/data/stats` | GET | 获取数据统计信息 |
| `/api/data/clear` | POST | 清空Elasticsearch数据 |
| `/api/data/import` | POST | 导入最新数据 |
| `/api/data/refresh` | POST | 完全刷新数据 |

### 🔧 关键词处理示例

```bash
# 单个关键词
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'

# 多个关键词（逗号分隔）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "备考,考试,诺石"}'

# 多个关键词（数组格式）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["执业药师", "备考", "考试"]}'

# 获取所有信息（不指定关键词）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### ✨ 中文字符处理特性
- **直接支持**：POST请求的JSON格式完美支持中文字符
- **无需编码**：不需要进行URL编码，直接使用原始中文字符
- **大小写不敏感**：关键词匹配不区分大小写
- **统一标准**：所有涉及中文的接口都使用POST方法

## 检索匹配逻辑

### AV号匹配方式：
1. **精确匹配** `av_number` 字段
2. **精确匹配** `video_id` 字段
3. **模糊匹配** `video_url` 字段（包含 `*av{number}*`）

### 评论组织逻辑：
1. **最新评论**：按时间倒序，不区分一级二级评论
2. **关键词评论组**：
   - 先获取所有评论
   - 按 `parent_comment_id` 组织评论结构
   - 筛选包含关键词的评论组
   - 按主评论时间排序
   - 返回完整的评论组（主评论+回复）

## 数据处理特点

- **时间格式化**：时间戳转换为ISO格式
- **重复率计算**：基于评论内容的相似度分析
- **分页支持**：所有列表接口支持分页
- **排序功能**：支持多字段排序
- **错误处理**：完整的错误响应机制

## 性能特点

- **索引优化**：基于Elasticsearch的高性能搜索
- **批量处理**：支持大量数据的批量导入
- **缓存机制**：减少重复查询的响应时间
- **限流保护**：API访问频率限制

## 示例用法

```bash
# 获取特定视频的完整信息（无关键词）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{}'

# 获取包含特定关键词的评论组
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "备考"}'

# 获取包含多个关键词的评论组（逗号分隔）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "备考,考试,诺石"}'

# 获取包含多个关键词的评论组（数组格式）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["备考", "考试", "诺石"]}'

# 中文关键词直接使用（完美支持）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'

# 获取包含关键词的评论（分页）
curl "http://localhost:3000/api/videos/113599301292265/comments?hasKeywords=true&page=1&limit=10"

# 搜索相关视频（POST方法，支持中文）
curl -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "page": 1, "limit": 10}'

# 全文搜索（POST方法，支持中文）
curl -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考", "type": "video", "page": 1, "limit": 10}'

# 获取热门视频
curl "http://localhost:3000/api/search/trending"
```

## 🚀 快速参考

### 最常用的命令

```bash
# 1. 获取视频信息（带中文关键词）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'

# 2. 搜索视频（中文搜索词）
curl -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考"}'

# 3. 全文搜索（中文搜索词）
curl -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "type": "all"}'
```

### 💡 使用提示

- ✅ **推荐**：使用POST方法处理所有中文关键词
- ✅ **优势**：无需URL编码，直接使用中文字符
- ✅ **格式**：支持字符串、数组、逗号分隔多种格式
- ✅ **兼容**：保留了GET方法以确保向后兼容

---

## 数据管理API

### 系统架构说明

**数据同步机制**：
- **数据源**：MySQL数据库 (**********:3307/media_crawler)
- **检索引擎**：Elasticsearch (localhost:9200)
- **同步方式**：手动触发（非实时同步）
- **批量处理**：每批处理1000条记录

**数据流向**：
```
MySQL (源数据库) → Elasticsearch (检索引擎)
- bilibili_video → bilibili_videos (索引)
- bilibili_video_comment → bilibili_comments (索引)
```

### 8. 检查数据库连接状态

```
GET /api/data/status
```

**功能**：检查MySQL和Elasticsearch的连接状态

**示例请求**：
```bash
curl -X GET "http://localhost:3000/api/data/status"
```

**返回示例**：
```json
{
  "success": true,
  "data": {
    "mysql": {
      "connected": true,
      "status": "connected"
    },
    "elasticsearch": {
      "connected": true,
      "status": "connected"
    }
  }
}
```

### 9. 获取数据统计信息

```
GET /api/data/stats
```

**功能**：获取MySQL和Elasticsearch中的数据统计，以及同步状态

**示例请求**：
```bash
curl -X GET "http://localhost:3000/api/data/stats"
```

**返回示例**：
```json
{
  "success": true,
  "data": {
    "mysql": {
      "videos": 45,
      "comments": 4105,
      "total": 4150
    },
    "elasticsearch": {
      "videos": 45,
      "comments": 4105,
      "total": 4150
    },
    "sync_status": {
      "videos_synced": true,
      "comments_synced": true,
      "fully_synced": true
    }
  }
}
```

### 10. 清空Elasticsearch数据

```
POST /api/data/clear
```

**功能**：清空Elasticsearch中的所有索引数据（重建索引结构）

**示例请求**：
```bash
curl -X POST "http://localhost:3000/api/data/clear"
```

**返回示例**：
```json
{
  "success": true,
  "message": "Elasticsearch数据已清空",
  "data": {
    "videos": 0,
    "comments": 0,
    "cleared_at": "2025-07-07T12:00:00.000Z"
  }
}
```

### 11. 导入最新数据

```
POST /api/data/import
```

**功能**：从MySQL导入最新数据到Elasticsearch（增量导入）

**示例请求**：
```bash
curl -X POST "http://localhost:3000/api/data/import"
```

**返回示例**：
```json
{
  "success": true,
  "message": "数据导入完成",
  "data": {
    "import_result": {
      "videos": {
        "success": 45,
        "failed": 0,
        "total": 45
      },
      "comments": {
        "success": 4105,
        "failed": 0,
        "total": 4105
      }
    },
    "before_stats": {
      "videos": 0,
      "comments": 0
    },
    "after_stats": {
      "videos": 45,
      "comments": 4105
    },
    "imported_at": "2025-07-07T12:00:00.000Z"
  }
}
```

### 12. 完全刷新数据

```
POST /api/data/refresh
```

**功能**：清空Elasticsearch数据并重新导入所有数据（全量刷新）

**示例请求**：
```bash
curl -X POST "http://localhost:3000/api/data/refresh"
```

**返回示例**：
```json
{
  "success": true,
  "message": "数据刷新完成",
  "data": {
    "import_result": {
      "videos": {
        "success": 45,
        "failed": 0,
        "total": 45
      },
      "comments": {
        "success": 4105,
        "failed": 0,
        "total": 4105
      }
    },
    "final_stats": {
      "videos": 45,
      "comments": 4105
    },
    "refreshed_at": "2025-07-07T12:00:00.000Z"
  }
}
```

## 数据管理使用场景

### 1. 定期数据同步
```bash
# 检查同步状态
curl -X GET "http://localhost:3000/api/data/stats"

# 如果数据不同步，执行导入
curl -X POST "http://localhost:3000/api/data/import"
```

### 2. 完全重建索引
```bash
# 清空并重新导入所有数据
curl -X POST "http://localhost:3000/api/data/refresh"
```

### 3. 故障排查
```bash
# 检查连接状态
curl -X GET "http://localhost:3000/api/data/status"

# 查看数据统计
curl -X GET "http://localhost:3000/api/data/stats"
```

## 命令行工具

提供了便捷的Shell脚本 `数据管理示例.sh` 用于快速操作：

```bash
# 检查连接状态
./数据管理示例.sh status

# 查看数据统计
./数据管理示例.sh stats

# 清空数据
./数据管理示例.sh clear

# 导入数据
./数据管理示例.sh import

# 完全刷新
./数据管理示例.sh refresh
```

## 错误处理

所有数据管理API在出错时都会返回以下格式：
```json
{
  "success": false,
  "error": "错误描述",
  "details": "详细错误信息"
}
```

## 注意事项

1. **数据导入时间**：大量数据导入可能需要较长时间，请耐心等待
2. **并发限制**：避免同时执行多个数据导入操作
3. **数据一致性**：导入过程中避免修改MySQL数据
4. **监控建议**：定期检查同步状态，确保数据一致性

## 与n8n集成建议

在n8n工作流中，您可以：

1. **定时检查**：使用Cron节点定期调用 `/api/data/stats` 检查同步状态
2. **条件导入**：根据 `sync_status.fully_synced` 字段决定是否需要导入数据
3. **错误通知**：在导入失败时发送通知
4. **数据验证**：导入后验证数据完整性

---

## 🚀 完整API快速参考

### 核心检索API
```bash
# 1. 获取视频信息（带中文关键词）
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'

# 2. 搜索视频（中文搜索词）
curl -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石医考"}'

# 3. 全文搜索（中文搜索词）
curl -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "type": "all"}'

# 4. 获取评论列表
curl "http://localhost:3000/api/videos/113599301292265/comments?page=1&limit=20"

# 5. 获取视频统计
curl "http://localhost:3000/api/videos/113599301292265/stats"

# 6. 获取热门视频
curl "http://localhost:3000/api/search/trending"

# 7. 获取关键词统计
curl "http://localhost:3000/api/search/keywords"
```

### 数据管理API
```bash
# 8. 检查数据库连接状态
curl -X GET "http://localhost:3000/api/data/status"

# 9. 获取数据统计信息
curl -X GET "http://localhost:3000/api/data/stats"

# 10. 清空Elasticsearch数据
curl -X POST "http://localhost:3000/api/data/clear"

# 11. 导入最新数据
curl -X POST "http://localhost:3000/api/data/import"

# 12. 完全刷新数据
curl -X POST "http://localhost:3000/api/data/refresh"
```

### 命令行工具快速操作
```bash
# 使用便捷脚本
./数据管理示例.sh status    # 检查连接状态
./数据管理示例.sh stats     # 查看数据统计
./数据管理示例.sh clear     # 清空数据
./数据管理示例.sh import    # 导入数据
./数据管理示例.sh refresh   # 完全刷新
```

### 推荐的数据管理流程
1. **定期检查**：`GET /api/data/stats` 检查同步状态
2. **按需导入**：当 `fully_synced: false` 时，调用 `POST /api/data/import`
3. **故障恢复**：出现问题时使用 `POST /api/data/refresh` 完全重建
4. **监控告警**：集成到监控系统中，自动化数据同步流程

### 系统状态总览
- **数据源**：MySQL (**********:3307/media_crawler)
- **检索引擎**：Elasticsearch (localhost:9200)
- **当前数据量**：45个视频，4105条评论
- **同步方式**：手动触发（非实时）
- **批量大小**：1000条记录/批次
