#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Docker重启后部署脚本 ===${NC}"
echo

# 验证Docker镜像源配置
echo -e "${YELLOW}🔍 验证Docker镜像源配置...${NC}"
if docker info | grep -q "registry.cn-hangzhou.aliyuncs.com"; then
    echo -e "${GREEN}✅ 阿里云镜像源配置正确${NC}"
else
    echo -e "${RED}❌ 镜像源配置未生效，请确认Docker已重启${NC}"
    echo -e "${YELLOW}当前镜像源：${NC}"
    docker info | grep -A 5 "Registry Mirrors"
    exit 1
fi

# 清理现有容器
echo -e "${YELLOW}🧹 清理现有容器和卷...${NC}"
docker-compose down -v

# 清理Docker系统
echo -e "${YELLOW}🗑️  清理Docker缓存...${NC}"
docker system prune -f

# 重新构建并启动
echo -e "${YELLOW}🚀 重新构建并启动服务...${NC}"
docker-compose up -d --build

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 20

# 检查服务状态
echo -e "${YELLOW}📊 检查服务状态...${NC}"
docker-compose ps

# 检查Elasticsearch
echo -e "${YELLOW}🔍 检查Elasticsearch...${NC}"
for i in {1..6}; do
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待Elasticsearch启动... ($i/6)${NC}"
        sleep 10
    fi
done

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务...${NC}"
for i in {1..3}; do
    if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待API服务启动... ($i/3)${NC}"
        sleep 5
    fi
done

echo
echo -e "${GREEN}🎉 部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务地址：${NC}"
echo "• API服务: http://localhost:3000"
echo "• Elasticsearch: http://localhost:9200"
echo
echo -e "${BLUE}🔧 下一步操作：${NC}"
echo "• 检查连接: curl http://localhost:3000/api/data/status"
echo "• 导入数据: curl -X POST http://localhost:3000/api/data/refresh"
echo "• 查看日志: docker-compose logs -f"
