#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Bilibili数据检索系统 - 快速部署脚本 ===${NC}"
echo

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker运行正常${NC}"

# 检查Docker镜像源配置
echo -e "${YELLOW}📋 检查Docker镜像源配置...${NC}"
if docker info | grep -q "registry.cn-hangzhou.aliyuncs.com"; then
    echo -e "${GREEN}✅ 阿里云镜像源已配置${NC}"
else
    echo -e "${YELLOW}⚠️  建议配置阿里云镜像源以加速镜像拉取${NC}"
    echo -e "${BLUE}配置方法：${NC}"
    echo "1. 编辑 ~/.docker/daemon.json (macOS) 或 /etc/docker/daemon.json (Linux)"
    echo "2. 添加内容："
    echo '{"registry-mirrors": ["https://registry.cn-hangzhou.aliyuncs.com"]}'
    echo "3. 重启Docker服务"
    echo
    read -p "是否继续部署？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
docker-compose down -v

# 清理系统
echo -e "${YELLOW}🧹 清理Docker系统...${NC}"
docker system prune -f

# 构建并启动服务
echo -e "${YELLOW}🚀 构建并启动服务...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 检查服务状态
echo -e "${YELLOW}📊 检查服务状态...${NC}"
docker-compose ps

# 检查Elasticsearch健康状态
echo -e "${YELLOW}🔍 检查Elasticsearch健康状态...${NC}"
for i in {1..10}; do
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待Elasticsearch启动... ($i/10)${NC}"
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        echo -e "${RED}❌ Elasticsearch启动失败${NC}"
        echo -e "${YELLOW}查看日志：${NC}"
        docker-compose logs elasticsearch
        exit 1
    fi
done

# 检查API服务
echo -e "${YELLOW}🔍 检查API服务...${NC}"
for i in {1..5}; do
    if curl -s http://localhost:3000/api/data/status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务运行正常${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待API服务启动... ($i/5)${NC}"
        sleep 5
    fi
    
    if [ $i -eq 5 ]; then
        echo -e "${RED}❌ API服务启动失败${NC}"
        echo -e "${YELLOW}查看日志：${NC}"
        docker-compose logs api
        exit 1
    fi
done

# 显示服务信息
echo
echo -e "${GREEN}🎉 部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务信息：${NC}"
echo "• Elasticsearch: http://localhost:9200"
echo "• API服务: http://localhost:3000"
echo
echo -e "${BLUE}🔧 管理命令：${NC}"
echo "• 查看服务状态: docker-compose ps"
echo "• 查看日志: docker-compose logs -f"
echo "• 停止服务: docker-compose down"
echo "• 重启服务: docker-compose restart"
echo
echo -e "${BLUE}📊 数据管理：${NC}"
echo "• 检查连接状态: curl http://localhost:3000/api/data/status"
echo "• 查看数据统计: curl http://localhost:3000/api/data/stats"
echo "• 导入数据: curl -X POST http://localhost:3000/api/data/refresh"
echo
echo -e "${YELLOW}💡 提示：首次使用需要导入数据，请运行：${NC}"
echo "curl -X POST http://localhost:3000/api/data/refresh"
