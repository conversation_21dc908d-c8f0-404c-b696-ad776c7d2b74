#!/bin/bash

# 数据一致性测试脚本
# 用于测试修复后的数据导入和查询接口

API_BASE="http://1.15.151.6:3000/api"
TEST_VIDEO="113599301292265"
TEST_KEYWORD="执业药师"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_separator() {
    echo -e "${BLUE}================================================${NC}"
}

# 检查API状态
check_api_status() {
    print_info "检查API服务状态..."
    
    response=$(curl -s "$API_BASE/data/status")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "API服务正常"
        echo "$response" | jq '.'
    else
        print_error "API服务异常"
        echo "$response"
        exit 1
    fi
}

# 检查数据统计
check_data_stats() {
    print_info "检查数据统计..."
    
    response=$(curl -s "$API_BASE/data/stats")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "数据统计获取成功"
        echo "$response" | jq '.'
        
        # 检查同步状态
        fully_synced=$(echo "$response" | jq -r '.data.sync_status.fully_synced')
        if [ "$fully_synced" = "true" ]; then
            print_success "数据已完全同步"
        else
            print_warning "数据未完全同步"
        fi
    else
        print_error "获取数据统计失败"
        echo "$response"
    fi
}

# 测试优化索引重建
test_rebuild_optimized() {
    print_info "测试优化索引重建..."
    
    response=$(curl -s -X POST "$API_BASE/data/rebuild-optimized")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "优化索引重建成功"
        echo "$response" | jq '.'
    else
        print_error "优化索引重建失败"
        echo "$response"
    fi
}

# 测试优化查询接口
test_optimized_query() {
    print_info "测试优化查询接口..."
    
    response=$(curl -s -X POST "$API_BASE/videos/$TEST_VIDEO/keywords-optimized" \
        -H "Content-Type: application/json" \
        -d "{\"keywords\": [\"$TEST_KEYWORD\"]}")
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "优化查询成功"
        
        # 检查返回的数据
        video_title=$(echo "$response" | jq -r '.data.video.title // empty')
        keyword_comments_count=$(echo "$response" | jq -r '.data.keywordComments | length')
        search_method=$(echo "$response" | jq -r '.searchMethod // "optimized"')
        
        if [ -n "$video_title" ]; then
            print_success "视频信息: $video_title"
        else
            print_warning "视频信息为空"
        fi
        
        print_info "关键词评论数量: $keyword_comments_count"
        print_info "搜索方法: $search_method"
        
        if [ "$search_method" = "optimized" ]; then
            print_success "使用了优化搜索"
        else
            print_warning "回退到原始搜索方法"
        fi
    else
        print_error "优化查询失败"
        echo "$response"
    fi
}

# 测试标准查询接口
test_standard_query() {
    print_info "测试标准查询接口..."
    
    response=$(curl -s -X POST "$API_BASE/videos/$TEST_VIDEO/keywords" \
        -H "Content-Type: application/json" \
        -d "{\"keywords\": [\"$TEST_KEYWORD\"]}")
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "标准查询成功"
        
        # 检查返回的数据
        video_title=$(echo "$response" | jq -r '.data.video.title // empty')
        keyword_comments_count=$(echo "$response" | jq -r '.data.keywordComments | length')
        
        if [ -n "$video_title" ]; then
            print_success "视频信息: $video_title"
        else
            print_warning "视频信息为空"
        fi
        
        print_info "关键词评论数量: $keyword_comments_count"
    else
        print_error "标准查询失败"
        echo "$response"
    fi
}

# 比较查询结果
compare_query_results() {
    print_info "比较优化查询和标准查询结果..."
    
    # 优化查询
    optimized_response=$(curl -s -X POST "$API_BASE/videos/$TEST_VIDEO/keywords-optimized" \
        -H "Content-Type: application/json" \
        -d "{\"keywords\": [\"$TEST_KEYWORD\"]}")
    
    # 标准查询
    standard_response=$(curl -s -X POST "$API_BASE/videos/$TEST_VIDEO/keywords" \
        -H "Content-Type: application/json" \
        -d "{\"keywords\": [\"$TEST_KEYWORD\"]}")
    
    if echo "$optimized_response" | jq -e '.success' > /dev/null 2>&1 && \
       echo "$standard_response" | jq -e '.success' > /dev/null 2>&1; then
        
        optimized_count=$(echo "$optimized_response" | jq -r '.data.keywordComments | length')
        standard_count=$(echo "$standard_response" | jq -r '.data.keywordComments | length')
        
        print_info "优化查询结果数量: $optimized_count"
        print_info "标准查询结果数量: $standard_count"
        
        if [ "$optimized_count" = "$standard_count" ]; then
            print_success "查询结果数量一致"
        else
            print_warning "查询结果数量不一致"
        fi
    else
        print_error "查询比较失败"
    fi
}

# 主函数
main() {
    print_separator
    echo -e "${BLUE}🔍 数据一致性测试开始${NC}"
    print_separator
    
    check_api_status
    echo
    
    check_data_stats
    echo
    
    test_rebuild_optimized
    echo
    
    test_optimized_query
    echo
    
    test_standard_query
    echo
    
    compare_query_results
    echo
    
    print_separator
    echo -e "${GREEN}🎉 数据一致性测试完成${NC}"
    print_separator
}

# 执行主函数
main
