# version: '3.8'  # 已废弃，移除以避免警告

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: bilibili-elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=bilibili-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - bilibili-network

# Kibana服务已移除 - 不需要可视化界面

  api:
    build: .
    container_name: bilibili-api
    env_file:
      - .env
    environment:
      # Docker特定的环境变量覆盖
      - NODE_ENV=production
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    ports:
      - "3000:3000"
    networks:
      - bilibili-network
    depends_on:
      - elasticsearch
    volumes:
      - .:/app
      - /app/node_modules

volumes:
  elasticsearch-data:
    driver: local

networks:
  bilibili-network:
    driver: bridge
