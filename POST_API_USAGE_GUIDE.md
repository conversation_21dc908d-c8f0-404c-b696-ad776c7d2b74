# POST API 使用指南

## 🎯 快速开始

本系统已统一使用POST方法处理所有涉及中文字符的API请求，完美解决中文编码问题。

## 📋 主要接口

### 1. 获取视频信息和关键词评论
```bash
curl -X POST "http://localhost:3000/api/videos/113599301292265/keywords" \
  -H "Content-Type: application/json" \
  -d '{"keywords": "执业药师"}'
```

### 2. 搜索视频
```bash
curl -X POST "http://localhost:3000/api/videos/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "诺石", "page": 1, "limit": 10}'
```

### 3. 全文搜索
```bash
curl -X POST "http://localhost:3000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "执业药师", "type": "all", "page": 1, "limit": 10}'
```

## 🔧 关键词格式支持

### 单个关键词
```json
{"keywords": "执业药师"}
```

### 多个关键词（逗号分隔）
```json
{"keywords": "备考,考试,诺石"}
```

### 多个关键词（数组格式）
```json
{"keywords": ["执业药师", "备考", "考试"]}
```

### 获取所有信息（不指定关键词）
```json
{}
```

## ✅ 优势对比

| 特性 | GET方法 | POST方法 |
|------|---------|----------|
| 中文字符支持 | ❌ 需要URL编码 | ✅ 直接支持 |
| 参数复杂度 | ❌ 受URL长度限制 | ✅ 支持复杂结构 |
| 易用性 | ❌ 编码复杂 | ✅ 简单直观 |
| 兼容性 | ❌ 终端编码问题 | ✅ 完美兼容 |

## 🚀 测试脚本

运行测试脚本验证所有接口：
```bash
./test_post_apis.sh
```

## 📊 响应数据优化

- ✅ 移除了 `dailyComments` 数据，响应大小从18KB减少到11KB
- ✅ 保留了所有重要的统计信息
- ✅ 提高了API响应速度

## 🎉 总结

使用POST方法的API接口：
- 完美支持中文字符，无需任何编码
- 参数传递更加灵活和强大
- 避免了URL编码的复杂性
- 提供了更好的用户体验

推荐在所有涉及中文关键词的场景中使用POST方法！
