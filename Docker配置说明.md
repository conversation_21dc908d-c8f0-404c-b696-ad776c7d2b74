# Docker配置说明

## 🚀 国内源优化

### 当前配置状态

✅ **Docker镜像源**: 推荐配置阿里云镜像源加速
- **镜像源**: `https://registry.cn-hangzhou.aliyuncs.com`
- **Elasticsearch**: `docker.elastic.co/elasticsearch/elasticsearch:8.11.0`
- **Kibana**: `docker.elastic.co/kibana/kibana:8.11.0`
- **Node.js**: `node:18-alpine`

✅ **npm源**: 使用官方源（稳定性优先）
- **npm源**: `https://registry.npmjs.org`

### Docker镜像源配置（推荐）

**配置阿里云镜像源加速Docker镜像拉取：**

将 `docker-daemon.json` 文件内容复制到Docker配置文件：

**macOS**: `~/.docker/daemon.json`
**Linux**: `/etc/docker/daemon.json`
**Windows**: `%USERPROFILE%\.docker\daemon.json`

```json
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com"
  ]
}
```

配置后重启Docker服务：
```bash
# macOS/Windows: 重启Docker Desktop
# Linux:
sudo systemctl restart docker
```

验证配置：
```bash
docker info | grep -A 5 "Registry Mirrors"
```

### npm源详情
- **官方源**: `https://registry.npmjs.org` (默认官方源)

## 📋 环境变量配置

### .env文件在Docker中的使用

**问题**：原始配置中，`.env` 文件不会在Docker中自动生效，因为 `docker-compose.yml` 中的 `environment` 配置会覆盖 `.env` 文件。

**解决方案**：修改了 `docker-compose.yml` 配置：

```yaml
api:
  build: .
  container_name: bilibili-api
  env_file:
    - .env  # 使用.env文件
  environment:
    # 只覆盖Docker特定的配置
    - NODE_ENV=production
    - ELASTICSEARCH_URL=http://elasticsearch:9200
```

### 配置优先级

1. **docker-compose.yml 中的 environment** (最高优先级)
2. **env_file 指定的文件** (.env)
3. **Dockerfile 中的 ENV** (最低优先级)

### 当前配置结构

#### 本地开发 (.env)
```bash
# 本地开发配置
ELASTICSEARCH_URL=http://localhost:9200
MYSQL_HOST=**********
MYSQL_PORT=3307
# ... 其他配置
```

#### Docker环境 (.env.docker) - 可选
```bash
# Docker环境专用配置
ELASTICSEARCH_URL=http://elasticsearch:9200
MYSQL_HOST=**********
MYSQL_PORT=3307
# ... 其他配置
```

## 🛠️ 使用方法

### 启动服务
```bash
# 停止现有服务
docker-compose down

# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f api
```

### 使用不同环境配置
```bash
# 使用默认.env文件
docker-compose up -d

# 使用Docker专用配置
docker-compose --env-file .env.docker up -d
```

### 数据导入
```bash
# 等待服务启动后，导入数据
curl -X POST "http://localhost:3000/api/data/refresh"
```

## 📁 文件结构

```
.
├── .env                    # 本地开发配置
├── .env.docker            # Docker环境配置（可选）
├── .dockerignore           # Docker忽略文件
├── Dockerfile              # Docker镜像构建文件
├── docker-compose.yml      # Docker服务编排文件
└── src/                    # 应用源码
```

## 🔧 配置验证

### 检查环境变量是否生效
```bash
# 进入容器查看环境变量
docker exec -it bilibili-api env | grep MYSQL

# 检查应用配置
curl -X GET "http://localhost:3000/api/data/status"
```

### 常见问题排查

1. **环境变量不生效**
   - 检查 `docker-compose.yml` 中的 `env_file` 配置
   - 确认 `.env` 文件存在且格式正确

2. **镜像拉取慢**
   - 已配置国内镜像源，如仍然慢可尝试其他源：
     - 阿里云：`registry.cn-hangzhou.aliyuncs.com`
     - 腾讯云：`ccr.ccs.tencentyun.com`
     - 网易云：`hub-mirror.c.163.com`

3. **npm安装慢**
   - 已配置淘宝npm镜像源
   - 如需更换可修改Dockerfile中的registry地址

## 🎯 最佳实践

1. **环境分离**：
   - 本地开发使用 `.env`
   - 生产环境使用 `.env.production`
   - Docker环境使用 `.env.docker`

2. **安全考虑**：
   - 生产环境不要将 `.env` 文件包含在镜像中
   - 使用Docker secrets或环境变量注入敏感信息

3. **性能优化**：
   - 使用多阶段构建减小镜像大小
   - 合理使用 `.dockerignore` 排除不必要文件

## 📊 服务状态检查

```bash
# 检查所有服务状态
docker-compose ps

# 检查API服务日志
docker-compose logs api

# 检查Elasticsearch状态
curl http://localhost:9200/_cluster/health

# 检查应用连接状态
curl http://localhost:3000/api/data/status
```
